<?php
session_start();
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_test':
                $test_name = $_POST['test_name'];
                $test_name_kurdish = $_POST['test_name_kurdish'];
                $description = $_POST['description'];
                $questions = json_encode($_POST['questions'], JSON_UNESCAPED_UNICODE);
                $scoring_info = $_POST['scoring_info'];
                
                $stmt = $con->prepare("INSERT INTO tests (test_name, test_name_kurdish, description, questions, scoring_info) VALUES (?, ?, ?, ?, ?)");
                $stmt->bind_param("sssss", $test_name, $test_name_kurdish, $description, $questions, $scoring_info);
                
                if ($stmt->execute()) {
                    $success_message = "تاقیکردنەوە بە سەرکەوتوویی زیادکرا!";
                } else {
                    $error_message = "هەڵەیەک ڕوویدا لە زیادکردنی تاقیکردنەوە: " . $con->error;
                }
                break;
                
            case 'update_test':
                $test_id = $_POST['test_id'];
                $test_name = $_POST['test_name'];
                $test_name_kurdish = $_POST['test_name_kurdish'];
                $description = $_POST['description'];
                $questions = json_encode($_POST['questions'], JSON_UNESCAPED_UNICODE);
                $scoring_info = $_POST['scoring_info'];
                
                $stmt = $con->prepare("UPDATE tests SET test_name = ?, test_name_kurdish = ?, description = ?, questions = ?, scoring_info = ? WHERE id = ?");
                $stmt->bind_param("sssssi", $test_name, $test_name_kurdish, $description, $questions, $scoring_info, $test_id);
                
                if ($stmt->execute()) {
                    $success_message = "تاقیکردنەوە بە سەرکەوتوویی نوێکرایەوە!";
                } else {
                    $error_message = "هەڵەیەک ڕوویدا لە نوێکردنەوەی تاقیکردنەوە: " . $con->error;
                }
                break;
                
            case 'delete_test':
                $test_id = $_POST['test_id'];
                $stmt = $con->prepare("DELETE FROM tests WHERE id = ?");
                $stmt->bind_param("i", $test_id);
                
                if ($stmt->execute()) {
                    $success_message = "تاقیکردنەوە بە سەرکەوتوویی سڕایەوە!";
                } else {
                    $error_message = "هەڵەیەک ڕوویدا لە سڕینەوەی تاقیکردنەوە: " . $con->error;
                }
                break;
        }
    }
}

// Get all tests
$tests_result = $con->query("SELECT * FROM tests ORDER BY id");
$tests = [];
while ($row = $tests_result->fetch_assoc()) {
    $tests[] = $row;
}
?>

<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بەڕێوەبردنی تاقیکردنەوەکان - ئیلهامبەخشی دەروونی</title>
    <?php include 'head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(font/Rabar_22.ttf);
        }
        @font-face {
            font-family: RabarBold;
            src: url(font/Rabar_21.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .admin-header {
            background: linear-gradient(135deg, #265e6d, #2e7d8a);
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .test-card-header {
            background: linear-gradient(135deg, #ed7014, #ff8c00);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        .btn-primary {
            background: #265e6d;
            border-color: #265e6d;
        }
        .btn-primary:hover {
            background: #2e7d8a;
            border-color: #2e7d8a;
        }
        .btn-warning {
            background: #ed7014;
            border-color: #ed7014;
        }
        .btn-warning:hover {
            background: #ff8c00;
            border-color: #ff8c00;
        }
        .form-control, .form-select {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .question-input {
            margin-bottom: 10px;
        }
        .questions-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .admin-header {
                padding: 20px 0;
            }
            .admin-header h1 {
                font-size: 1.5rem;
            }
            .test-card {
                margin-bottom: 15px;
            }
            .test-card-header h5 {
                font-size: 1rem;
            }
            .card-body {
                padding: 15px !important;
            }
            .btn {
                font-size: 0.875rem;
                padding: 8px 12px;
            }
            .modal-dialog {
                margin: 10px;
            }
            .questions-container {
                max-height: 300px;
            }
            .question-input .input-group {
                flex-wrap: wrap;
            }
            .question-input .form-control {
                min-width: 200px;
            }
            .question-input .btn {
                margin-top: 5px;
                width: 100%;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding: 0 10px;
            }
            .col-md-6 {
                padding: 0 5px;
            }
            .test-card {
                margin-bottom: 10px;
            }
            .d-flex.gap-2 {
                flex-direction: column;
                gap: 10px !important;
            }
            .btn-sm {
                padding: 10px;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'admin_navbar.php'; ?>

    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">بەڕێوەبردنی تاقیکردنەوەکان</h1>
                    <p class="mb-0 opacity-75">زیادکردن، دەستکاری، و بەڕێوەبردنی تاقیکردنەوە دەروونییەکان</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white">
                        <i class="fas fa-clipboard-list fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Add New Test Button -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addTestModal">
                    <i class="fas fa-plus me-2"></i> زیادکردنی تاقیکردنەوەی نوێ
                </button>
            </div>
        </div>

        <!-- Tests List -->
        <div class="row">
            <?php if (empty($tests)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">هیچ تاقیکردنەوەیەک نەدۆزرایەوە</h4>
                        <p class="text-muted">یەکەمین تاقیکردنەوەت زیاد بکە</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTestModal">
                            <i class="fas fa-plus me-2"></i> زیادکردنی تاقیکردنەوەی یەکەم
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($tests as $test): ?>
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <div class="test-card">
                            <div class="test-card-header">
                                <h5 class="mb-0"><?php echo htmlspecialchars($test['test_name_kurdish']); ?></h5>
                            </div>
                            <div class="card-body p-3">
                                <p class="text-muted mb-2 small">
                                    <strong>ناوی ئینگلیزی:</strong> <?php echo htmlspecialchars($test['test_name']); ?>
                                </p>
                                <p class="mb-2 small">
                                    <strong>وەسف:</strong> <?php echo htmlspecialchars(substr($test['description'], 0, 50)) . (strlen($test['description']) > 50 ? '...' : ''); ?>
                                </p>
                                <p class="mb-2 small">
                                    <strong>ژمارەی پرسیار:</strong>
                                    <span class="badge bg-info"><?php echo count(json_decode($test['questions'], true)); ?></span>
                                </p>
                                <p class="mb-3 small">
                                    <strong>زانیاری نمرەدان:</strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars(substr($test['scoring_info'], 0, 60)) . (strlen($test['scoring_info']) > 60 ? '...' : ''); ?></small>
                                </p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-warning btn-sm flex-fill" onclick="editTest(<?php echo $test['id']; ?>)">
                                        <i class="fas fa-edit me-1"></i> دەستکاری
                                    </button>
                                    <button class="btn btn-danger btn-sm flex-fill" onclick="deleteTest(<?php echo $test['id']; ?>)">
                                        <i class="fas fa-trash me-1"></i> سڕینەوە
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Test Modal -->
    <div class="modal fade" id="addTestModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">زیادکردنی تاقیکردنەوەی نوێ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_test">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ناوی تاقیکردنەوە (کوردی)</label>
                                    <input type="text" class="form-control" name="test_name_kurdish" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ناوی تاقیکردنەوە (ئینگلیزی)</label>
                                    <input type="text" class="form-control" name="test_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وەسف</label>
                            <textarea class="form-control" name="description" rows="2" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">پرسیارەکان</label>
                            <div class="questions-container" id="questionsContainer">
                                <div class="question-input">
                                    <div class="input-group">
                                        <span class="input-group-text">1</span>
                                        <input type="text" class="form-control" name="questions[]" placeholder="پرسیاری یەکەم..." required>
                                        <button type="button" class="btn btn-outline-danger" onclick="removeQuestion(this)" disabled>
                                            سڕینەوە
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary mt-2" onclick="addQuestion()">
                                زیادکردنی پرسیار
                            </button>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">زانیاری نمرەدان</label>
                            <textarea class="form-control" name="scoring_info" rows="3"
                                placeholder="نمرە 0-10: ..., 11-25: ..., 26-40: ..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">پاشگەزبوونەوە</button>
                        <button type="submit" class="btn btn-primary">زیادکردن</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Test Modal -->
    <div class="modal fade" id="editTestModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">دەستکاریکردنی تاقیکردنەوە</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editTestForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_test">
                        <input type="hidden" name="test_id" id="editTestId">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ناوی تاقیکردنەوە (کوردی)</label>
                                    <input type="text" class="form-control" name="test_name_kurdish" id="editTestNameKurdish" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ناوی تاقیکردنەوە (ئینگلیزی)</label>
                                    <input type="text" class="form-control" name="test_name" id="editTestName" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وەسف</label>
                            <textarea class="form-control" name="description" id="editDescription" rows="2" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">پرسیارەکان</label>
                            <div class="questions-container" id="editQuestionsContainer">
                                <!-- Questions will be loaded here -->
                            </div>
                            <button type="button" class="btn btn-outline-primary mt-2" onclick="addEditQuestion()">
                                زیادکردنی پرسیار
                            </button>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">زانیاری نمرەدان</label>
                            <textarea class="form-control" name="scoring_info" id="editScoringInfo" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">پاشگەزبوونەوە</button>
                        <button type="submit" class="btn btn-warning">نوێکردنەوە</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include 'script.php'; ?>
    <script>
        // Test data for JavaScript
        const tests = <?php echo json_encode($tests, JSON_UNESCAPED_UNICODE); ?>;

        let questionCounter = 1;
        let editQuestionCounter = 1;

        // Add new question input
        function addQuestion() {
            questionCounter++;
            const container = document.getElementById('questionsContainer');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-input';
            questionDiv.innerHTML = `
                <div class="input-group">
                    <span class="input-group-text">${questionCounter}</span>
                    <input type="text" class="form-control" name="questions[]" placeholder="پرسیاری ${questionCounter}..." required>
                    <button type="button" class="btn btn-outline-danger" onclick="removeQuestion(this)">
                        سڕینەوە
                    </button>
                </div>
            `;
            container.appendChild(questionDiv);
            updateQuestionNumbers();
        }

        // Remove question input
        function removeQuestion(button) {
            const questionDiv = button.closest('.question-input');
            questionDiv.remove();
            updateQuestionNumbers();
        }

        // Update question numbers
        function updateQuestionNumbers() {
            const questions = document.querySelectorAll('#questionsContainer .question-input');
            questions.forEach((question, index) => {
                const numberSpan = question.querySelector('.input-group-text');
                const input = question.querySelector('input');
                const button = question.querySelector('.btn-outline-danger');

                numberSpan.textContent = index + 1;
                input.placeholder = `پرسیاری ${index + 1}...`;

                // Disable remove button if only one question
                button.disabled = questions.length === 1;
            });
            questionCounter = questions.length;
        }

        // Add question for edit modal
        function addEditQuestion() {
            editQuestionCounter++;
            const container = document.getElementById('editQuestionsContainer');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-input';
            questionDiv.innerHTML = `
                <div class="input-group">
                    <span class="input-group-text">${editQuestionCounter}</span>
                    <input type="text" class="form-control" name="questions[]" placeholder="پرسیاری ${editQuestionCounter}..." required>
                    <button type="button" class="btn btn-outline-danger" onclick="removeEditQuestion(this)">
                        سڕینەوە
                    </button>
                </div>
            `;
            container.appendChild(questionDiv);
            updateEditQuestionNumbers();
        }

        // Remove question from edit modal
        function removeEditQuestion(button) {
            const questionDiv = button.closest('.question-input');
            questionDiv.remove();
            updateEditQuestionNumbers();
        }

        // Update question numbers in edit modal
        function updateEditQuestionNumbers() {
            const questions = document.querySelectorAll('#editQuestionsContainer .question-input');
            questions.forEach((question, index) => {
                const numberSpan = question.querySelector('.input-group-text');
                const input = question.querySelector('input');
                const button = question.querySelector('.btn-outline-danger');

                numberSpan.textContent = index + 1;
                input.placeholder = `پرسیاری ${index + 1}...`;

                // Disable remove button if only one question
                button.disabled = questions.length === 1;
            });
            editQuestionCounter = questions.length;
        }

        // Edit test function
        function editTest(testId) {
            const test = tests.find(t => t.id == testId);
            if (!test) return;

            // Fill form fields
            document.getElementById('editTestId').value = test.id;
            document.getElementById('editTestNameKurdish').value = test.test_name_kurdish;
            document.getElementById('editTestName').value = test.test_name;
            document.getElementById('editDescription').value = test.description;
            document.getElementById('editScoringInfo').value = test.scoring_info;

            // Load questions
            const questions = JSON.parse(test.questions);
            const container = document.getElementById('editQuestionsContainer');
            container.innerHTML = '';

            questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-input';
                questionDiv.innerHTML = `
                    <div class="input-group">
                        <span class="input-group-text">${index + 1}</span>
                        <input type="text" class="form-control" name="questions[]" value="${question}" required>
                        <button type="button" class="btn btn-outline-danger" onclick="removeEditQuestion(this)" ${questions.length === 1 ? 'disabled' : ''}>
                            سڕینەوە
                        </button>
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            editQuestionCounter = questions.length;

            // Show modal
            new bootstrap.Modal(document.getElementById('editTestModal')).show();
        }

        // Delete test function
        function deleteTest(testId) {
            const test = tests.find(t => t.id == testId);
            if (!test) return;

            if (confirm(`ئایا دڵنیایت لە سڕینەوەی تاقیکردنەوەی "${test.test_name_kurdish}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_test">
                    <input type="hidden" name="test_id" value="${testId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Initialize question numbers on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateQuestionNumbers();
        });
    </script>
</body>
</html>
