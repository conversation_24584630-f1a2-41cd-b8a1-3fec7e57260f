<?php
// Start the session to check if the user is logged in
include 'db.php';

// Get the current day and time
$currentDay = date('l'); // E.g., "Monday", "Tuesday"
$currentTime = date('H:i:s');

// Fetch Doctors, Psychologists, and Counsellors with their schedules
$query = "
    SELECT u.id, u.username, u.email, u.profile_photo, u.role, s.start_time, s.end_time
    FROM users u
    LEFT JOIN users_schedule s ON u.id = s.user_id AND s.day_of_week = ?
    WHERE u.role IN ('Doctor', 'Psychologist', 'Counsellor')
";
$stmt = $con->prepare($query);
$stmt->bind_param("s", $currentDay);
$stmt->execute();
$result = $stmt->get_result();
?>

<div class="container">
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3">
        <?php while ($row = $result->fetch_assoc()): ?>
            <?php
            // Determine if the user is currently "online" based on the schedule
            $isOnline = false;
            if (!empty($row['start_time']) && !empty($row['end_time'])) {
                $isOnline = ($currentTime >= $row['start_time'] && $currentTime <= $row['end_time']);
            }
            ?>
            <div class="col my-3 mx-md-3">
                <div class="contact-card">
                    <div class="status-indicator <?php echo $isOnline ? 'online' : 'offline'; ?>"></div>
                    <div class="profile-section">
                        <img src="<?php echo !empty($row['profile_photo']) ? $row['profile_photo'] : 'img/unknown.png'; ?>" alt="Profile Picture">
                        <h2><?php echo htmlspecialchars($row['username']); ?></h2>

                        <!-- Conditionally show email if the user is logged in -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <p><?php echo htmlspecialchars($row['email']); ?></p>
                        <?php else: ?>
                            <p class="text-muted"></p>
                        <?php endif; ?>

                        <!-- Button to View Schedule -->
                        <button class="btn btn-primary view-schedule-button button-teal" data-user-id="<?php echo $row['id']; ?>" data-username="<?php echo htmlspecialchars($row['username']); ?>" data-bs-toggle="modal" data-bs-target="#userScheduleModal">خشتەکەی ببینە</button>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
</div>



<!-- Schedule Modal -->
<div class="modal fade" id="userScheduleModal" tabindex="-1" aria-labelledby="userScheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userScheduleModalLabel"></h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Schedule Content will be loaded here dynamically -->
                <div id="scheduleContent">گەڕان بەدوای خشتەکە...</div>
            </div>
        </div>
    </div>
</div>

<?php
// Close the database connection
$con->close();
?>