<?php
// Fix scoring_info numbers from Kurdish-Indic to English numerals
require_once 'db.php';

// Kurdish-Indic to English number mapping
$kurdish_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
$english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

// Get all tests
$result = $con->query("SELECT id, scoring_info FROM tests");

echo "Fixing numbers in scoring_info...\n";

while ($test = $result->fetch_assoc()) {
    $original = $test['scoring_info'];
    $fixed = str_replace($kurdish_numbers, $english_numbers, $original);

    if ($original !== $fixed) {
        $update_stmt = $con->prepare("UPDATE tests SET scoring_info = ? WHERE id = ?");
        $update_stmt->bind_param("si", $fixed, $test['id']);
        $update_stmt->execute();
        echo "Fixed test ID {$test['id']}: {$original} -> {$fixed}\n";
    } else {
        echo "Test ID {$test['id']}: No changes needed\n";
    }
}

echo "\nAll numbers have been converted to English numerals!\n";

// Display updated results
echo "\nUpdated scoring_info:\n";
$result = $con->query("SELECT id, test_name_kurdish, scoring_info FROM tests");
while ($row = $result->fetch_assoc()) {
    echo "ID {$row['id']}: {$row['test_name_kurdish']}\n";
    echo "Scoring: {$row['scoring_info']}\n\n";
}

$con->close();
?>
