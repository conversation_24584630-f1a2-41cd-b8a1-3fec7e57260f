<style>
@font-face {
    font-family: Ra<PERSON>;
    src: url(font/Rabar_22.ttf);
}

.navbar {
    font-family: Ra<PERSON>, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #265e6d, #2e7d8a) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0;
}

.navbar .container {
    padding: 0 15px;
}

body {
    padding-top: 0;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-brand img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 5px;
    padding: 8px 16px !important;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(237, 112, 20, 0.8);
    color: white !important;
}
</style>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="admin.php">
            <img src="img/logo.png" alt="Logo">
            پانێڵی بەڕێوەبەر
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
            aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="admin.php">
                        <i class="fas fa-home me-1"></i>
                        سەرەکی
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_users.php">
                        <i class="fas fa-users me-1"></i>
                        بەکارهێنەران
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_tests.php">
                        <i class="fas fa-clipboard-list me-1"></i>
                        تاقیکردنەوەکان
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_transactions.php">
                        <i class="fas fa-exchange-alt me-1"></i>
                        مامەڵەکان
                    </a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="logout.php">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        چوونەدەرەوە
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
// Add active class to current page
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop();
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'admin.php')) {
            link.classList.add('active');
        }
    });
});
</script>