<?php
header('Content-Type: text/html; charset=utf-8');
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "inspiremental";

try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Get test ID from URL parameter
$test_id = isset($_GET['test_id']) ? (int)$_GET['test_id'] : 1;

// Fetch test data from database
try {
    $stmt = $pdo->prepare("SELECT * FROM tests WHERE id = ?");
    $stmt->execute([$test_id]);
    $test = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$test) {
        die("Test not found");
    }
    
    $questions = json_decode($test['questions'], true);
    if (!$questions) {
        die("Invalid test data");
    }
    
} catch(PDOException $e) {
    die("Error fetching test: " . $e->getMessage());
}

// Handle test submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $answers = json_decode(file_get_contents('php://input'), true);
    
    if ($answers && isset($answers['scores'])) {
        $total_score = array_sum($answers['scores']);
        
        // Save test result to database (optional - only if user is logged in)
        session_start();
        if (isset($_SESSION['user_id'])) {
            try {
                $stmt = $pdo->prepare("INSERT INTO test_results (user_id, test_id, answers, total_score) VALUES (?, ?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], $test_id, json_encode($answers['scores']), $total_score]);
            } catch(PDOException $e) {
                // Log error but don't fail the response
                error_log("Error saving test result: " . $e->getMessage());
            }
        }
        
        // Return result
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'total_score' => $total_score,
            'max_score' => count($questions) * 4,
            'scoring_info' => $test['scoring_info']
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ckb">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($test['test_name_kurdish']); ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            width: 90%;
            min-width: 300px;
            max-width: 800px;
            background: #fff;
            padding: 0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            min-height: 600px;
        }
        .progress-bar {
            height: 8px;
            background: #e0e0e0;
            border-radius: 0;
            overflow: hidden;
        }
        .progress-bar .progress {
            height: 100%;
            background: linear-gradient(90deg, #ed7014, #ff8c00);
            width: 0%;
            transition: width 0.4s ease;
        }
        .testTitle {
            text-align: center;
            background: linear-gradient(135deg, #265e6d, #2e7d8a);
            color: white;
            padding: 20px;
            margin: 0;
            font-size: 1.5em;
            font-weight: 600;
        }
        .question-container {
            position: relative;
            width: 100%;
            height: 500px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .question {
            background: rgba(255, 255, 255, 0.95);
            font-size: 1.3em;
            text-align: center;
            position: absolute;
            padding: 40px 30px;
            width: calc(100% - 60px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: transform 0.5s ease, opacity 0.5s ease;
            color: #333;
            line-height: 1.6;
        }
        .options {
            width: 100%;
            margin-top: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: row;
            gap: 15px;
            flex-wrap: wrap;
        }
        .options button {
            flex: 1;
            min-width: 120px;
            max-width: 150px;
            padding: 15px 10px;
            font-size: 0.9em;
            color: #555;
            background: rgba(237, 112, 20, 0.1);
            border: 2px solid rgba(237, 112, 20, 0.3);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .options button:hover {
            background: #ed7014;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(237, 112, 20, 0.3);
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }
        .navigation button {
            padding: 12px 25px;
            font-size: 1em;
            background: #265e6d;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .navigation button:hover:not(:disabled) {
            background: #2e7d8a;
            transform: translateY(-1px);
        }
        .navigation button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        #slide {
            font-weight: 600;
            color: #666;
        }
        .result-container {
            text-align: center;
            padding: 40px 30px;
            color: #333;
        }
        .result-container h2 {
            color: #265e6d;
            margin-bottom: 20px;
        }
        .score-display {
            font-size: 2em;
            font-weight: bold;
            color: #ed7014;
            margin: 20px 0;
        }
        .scoring-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ed7014;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                min-height: 500px;
            }
            .options {
                flex-direction: column;
                gap: 10px;
            }
            .options button {
                width: 80%;
                max-width: none;
            }
            .question {
                font-size: 1.1em;
                padding: 30px 20px;
            }
            .navigation {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h3 class="testTitle"><?php echo htmlspecialchars($test['test_name_kurdish']); ?></h3>
        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>
        <div class="question-container" id="question-container">
            <!-- Questions will be dynamically inserted here -->
        </div>
        <div class="navigation">
            <button id="prev" disabled>پێشوو</button>
            <p id="slide">پرسیار 1 لە <?php echo count($questions); ?></p>
            <button id="next" disabled>دواتر</button>
        </div>
    </div>

    <script>
    const questions = <?php echo json_encode($questions, JSON_UNESCAPED_UNICODE); ?>;
    const testId = <?php echo $test_id; ?>;
    const scoringInfo = <?php echo json_encode($test['scoring_info'], JSON_UNESCAPED_UNICODE); ?>;
    
    const scores = Array(questions.length).fill(null);
    let currentQuestion = 0;
    let highestReachedQuestion = 0;

    const progressElement = document.getElementById("progress");
    const questionContainer = document.getElementById("question-container");
    const prevButton = document.getElementById("prev");
    const nextButton = document.getElementById("next");
    const slideCounter = document.getElementById("slide");

    function renderQuestion() {
        // Clear existing question and options
        questionContainer.innerHTML = "";

        // Create question element
        const questionElement = document.createElement("div");
        questionElement.classList.add("question");
        questionElement.textContent = questions[currentQuestion];
        questionElement.style.transform = "translateX(100%)";
        questionContainer.appendChild(questionElement);

        // Create options
        const optionsElement = document.createElement("div");
        optionsElement.classList.add("options");
        ["هەرگیز", "جارێک", "چەند جار", "زۆر جار", "هەمیشە"].forEach((option, index) => {
            const button = document.createElement("button");
            button.textContent = option;
            button.onclick = () => selectAnswer(index);
            optionsElement.appendChild(button);
        });
        optionsElement.style.transform = "translateX(100%)";
        questionContainer.appendChild(optionsElement);

        // Trigger sliding animation
        setTimeout(() => {
            questionElement.style.transform = "translateX(0)";
            optionsElement.style.transform = "translateX(0)";
        }, 10);

        // Update progress bar
        progressElement.style.width = `${((currentQuestion + 1) / questions.length) * 100}%`;

        // Update navigation buttons and slide counter
        prevButton.disabled = currentQuestion === 0;
        nextButton.disabled = scores[currentQuestion] === null;
        slideCounter.textContent = `پرسیار ${currentQuestion + 1} لە ${questions.length}`;
    }

    function slideOut(direction, callback) {
        const questionElement = questionContainer.querySelector(".question");
        const optionsElement = questionContainer.querySelector(".options");

        const outgoingOffset = direction === "left" ? "100%" : "-100%";

        // Slide out current question
        questionElement.style.transform = `translateX(${outgoingOffset})`;
        optionsElement.style.transform = `translateX(${outgoingOffset})`;

        setTimeout(() => {
            callback();
        }, 500);
    }

    function selectAnswer(index) {
        scores[currentQuestion] = index;

        // Update the highest reached question
        if (currentQuestion >= highestReachedQuestion) {
            highestReachedQuestion = currentQuestion + 1;
        }

        nextButton.disabled = false;

        if (currentQuestion < questions.length - 1) {
            slideOut("left", () => {
                currentQuestion++;
                renderQuestion();
            });
        } else {
            showResults();
        }
    }

    prevButton.onclick = () => {
        if (currentQuestion > 0) {
            slideOut("right", () => {
                currentQuestion--;
                renderQuestion();
            });
        }
    };

    nextButton.onclick = () => {
        if (currentQuestion < questions.length - 1) {
            slideOut("left", () => {
                currentQuestion++;
                if (currentQuestion > highestReachedQuestion) {
                    highestReachedQuestion = currentQuestion;
                }
                renderQuestion();
            });
        } else {
            showResults();
        }
    };

    function showResults() {
        // Send results to server
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                scores: scores,
                test_id: testId
            })
        })
        .then(response => response.json())
        .then(data => {
            slideOut("left", () => {
                questionContainer.innerHTML = `
                    <div class="result-container">
                        <h2>تەواو بوو!</h2>
                        <div class="score-display">${data.total_score} لە ${data.max_score}</div>
                        <div class="scoring-info">
                            <strong>لێکدانەوەی ئەنجام:</strong><br>
                            ${data.scoring_info}
                        </div>
                        <button onclick="window.history.back()" style="background: #ed7014; color: white; border: none; padding: 15px 30px; border-radius: 25px; cursor: pointer; font-size: 1em; margin-top: 20px;">گەڕانەوە</button>
                    </div>
                `;
                
                // Hide navigation
                document.querySelector('.navigation').style.display = 'none';
            });
        })
        .catch(error => {
            console.error('Error:', error);
            alert('هەڵەیەک ڕوویدا لە پاشەکەوتکردنی ئەنجامەکان');
        });
    }

    // Initial render
    renderQuestion();
    </script>
</body>
</html>
