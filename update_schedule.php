<?php
session_start();
include 'db.php'; // Make sure this includes your database connection code

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Ensure the user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in.']);
        exit;
    }

    // Get user ID from session
    $user_id = $_SESSION['user_id'];

    // Retrieve and sanitize inputs
    $day_of_week = $_POST['day_of_week'];
    $start_times = $_POST['start_time']; // Array of start times
    $end_times = $_POST['end_time'];     // Array of end times

    // Validate that both arrays have the same length
    if (count($start_times) != count($end_times)) {
        echo json_encode(['success' => false, 'message' => 'Mismatched start and end times.']);
        exit;
    }

    // Delete existing schedule entries for the user and selected day
    $stmt = $con->prepare("DELETE FROM users_schedule WHERE user_id = ? AND day_of_week = ?");
    $stmt->bind_param("is", $user_id, $day_of_week);
    $stmt->execute();
    $stmt->close();

    // Prepare insert statement
    $stmt = $con->prepare("INSERT INTO users_schedule (user_id, day_of_week, start_time, end_time) VALUES (?, ?, ?, ?)");

    // Insert each time slot
    foreach ($start_times as $index => $start_time) {
        $end_time = $end_times[$index];
        // Validate that end time is after start time
        if ($end_time <= $start_time) {
            echo json_encode(['success' => false, 'message' => 'End time must be after start time for all time slots.']);
            exit;
        }
        $stmt->bind_param("isss", $user_id, $day_of_week, $start_time, $end_time);
        $stmt->execute();
    }

    $stmt->close();
    $con->close();

    echo json_encode(['success' => true, 'message' => 'Schedule updated successfully.']);
}
?>
