<div class="modern-chat-wrapper">
    <!-- Language Toggle -->
    <div class="language-toggle">
        <button id="lang-en" class="lang-btn active" onclick="switchLanguage('en')">English</button>
        <button id="lang-ckb" class="lang-btn" onclick="switchLanguage('ckb')">کوردی</button>
    </div>

    <!-- Chat Container -->
    <div class="modern-chat-container">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="bot-avatar">
                <img src="img/bot.png" alt="AI Assistant"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="avatar-fallback">🤖</div>
            </div>
            <div class="bot-info">
                <h3 id="bot-name">Mental Health Assistant</h3>
                <span id="bot-status" class="online-status">Online</span>
            </div>
            <div class="chat-actions">
                <button id="clear-chat" class="action-btn" title="Clear Chat">🗑️</button>
                <button id="minimize-chat" class="action-btn" title="Minimize">➖</button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="chat-messages" class="modern-chat-messages">
            <div class="welcome-message">
                <div class="bot-avatar-small">🤖</div>
                <div class="message-content">
                    <p id="welcome-text">Hello! I'm your mental health support assistant. How can I help you today?</p>
                    <div class="suggested-questions" id="suggested-questions">
                        <!-- Suggested questions will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="modern-chat-input">
            <div class="input-container">
                <input type="text" id="user-message" placeholder="Type your message..." autocomplete="off">
                <button id="send-btn" class="send-button" onclick="sendMessage()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                </button>
            </div>
            <div class="input-footer">
                <small id="input-hint">Press Enter to send • Powered by AI</small>
            </div>
        </div>
    </div>
</div>

<script>
// Language support
let currentLanguage = 'en';

const translations = {
    en: {
        botName: "Mental Health Assistant",
        botStatus: "Online",
        welcomeText: "Hello! I'm your mental health support assistant. How can I help you today?",
        placeholder: "Type your message...",
        inputHint: "Press Enter to send • Powered by AI",
        typing: "Assistant is typing...",
        questions: [
            "What is depression?",
            "How to cope with anxiety?",
            "Can you explain bipolar disorder?",
            "What are the symptoms of PTSD?",
            "How to manage stress?",
            "Tell me about panic attacks",
            "What is social anxiety?",
            "How to improve sleep?"
        ]
    },
    ckb: {
        botName: "یاریدەدەری تەندروستی دەروونی",
        botStatus: "سەرهێڵ",
        welcomeText: "سڵاو! من یاریدەدەری تەندروستی دەروونیتم. چۆن دەتوانم یارمەتیت بدەم؟",
        placeholder: "پەیامەکەت بنووسە...",
        inputHint: "Enter دابگرە بۆ ناردن • بە هێزی AI",
        typing: "یاریدەدەر دەنووسێت...",
        questions: [
            "خەمۆکی چییە؟",
            "چۆن دەتوانم دڵەڕاوکێ کۆنترۆڵ بکەم؟",
            "نەخۆشی دوو قۆناغی چییە؟",
            "نیشانەکانی PTSD چین؟",
            "چۆن فشاری دەروونی بەڕێوە ببەم؟",
            "دەربارەی هێرشی تۆقین بڵێ",
            "دڵەڕاوکێی کۆمەڵایەتی چییە؟",
            "چۆن خەوم باشتر بکەم؟"
        ]
    }
};

const questions = translations[currentLanguage].questions;

let chatMessages = document.getElementById('chat-messages');
let messageCount = 0;

// Language switching function
function switchLanguage(lang) {
    currentLanguage = lang;

    // Update UI elements
    document.getElementById('bot-name').textContent = translations[lang].botName;
    document.getElementById('bot-status').textContent = translations[lang].botStatus;
    document.getElementById('welcome-text').textContent = translations[lang].welcomeText;
    document.getElementById('user-message').placeholder = translations[lang].placeholder;
    document.getElementById('input-hint').textContent = translations[lang].inputHint;

    // Update language buttons
    document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`lang-${lang}`).classList.add('active');

    // Regenerate suggested questions
    generateSuggestedQuestions();
}

// Generate suggested questions
function generateSuggestedQuestions() {
    const container = document.getElementById('suggested-questions');
    container.innerHTML = '';

    const questions = translations[currentLanguage].questions;
    const randomQuestions = getRandomQuestions(questions, 4);

    randomQuestions.forEach(question => {
        const btn = document.createElement('button');
        btn.className = 'suggested-question';
        btn.textContent = question;
        btn.onclick = () => sendSuggestedQuestion(question);
        container.appendChild(btn);
    });
}

function getRandomQuestions(questionsArray, count) {
    const shuffled = [...questionsArray].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}

// Send suggested question
function sendSuggestedQuestion(question) {
    document.getElementById('user-message').value = question;
    sendMessage();
}

// Add message to chat
function addMessage(content, isUser = false, isTyping = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${isUser ? 'user-message' : 'bot-message'}`;

    if (isTyping) {
        messageDiv.id = 'typing-indicator';
        messageDiv.classList.add('typing');
    }

    const timestamp = new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.innerHTML = `
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
    return messageDiv;
}

// Clear chat function
function clearChat() {
    const messages = chatMessages.querySelectorAll('.chat-message');
    messages.forEach(msg => msg.remove());
    generateSuggestedQuestions();
}

function sendMessage() {
    const userMessage = document.getElementById('user-message').value.trim();
    if (userMessage === "") return;

    // Add user message
    addMessage(userMessage, true);
    document.getElementById('user-message').value = "";

    // Hide suggested questions after first message
    const suggestedQuestions = document.getElementById('suggested-questions');
    if (suggestedQuestions && messageCount === 0) {
        suggestedQuestions.style.display = 'none';
    }
    messageCount++;

    respondToUser(userMessage);
}

async function respondToUser(userMessage) {
    // Show typing indicator
    const typingMessage = addMessage(translations[currentLanguage].typing, false, true);

    try {
        // Translate message if needed
        let messageToSend = userMessage;
        if (currentLanguage === 'ckb') {
            messageToSend = await translateText(userMessage, 'ckb', 'en');
        }

        // Send message to Gemini API via PHP backend
        const response = await fetch('gemini_chat.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: messageToSend,
                language: currentLanguage
            })
        });

        const data = await response.json();

        // Remove typing indicator
        typingMessage.remove();

        if (data.success) {
            let responseText = data.response;

            // Translate response back if needed
            if (currentLanguage === 'ckb') {
                responseText = await translateText(responseText, 'en', 'ckb');
            }

            addMessage(responseText, false);
        } else {
            const errorMsg = currentLanguage === 'ckb' ?
                'ببوورە، ئێستا کێشەم هەیە لە وەڵامدانەوە. تکایە دووبارە هەوڵ بدەوە.' :
                "I'm sorry, I'm having trouble responding right now. Please try again later.";
            addMessage(errorMsg, false);
        }
    } catch (error) {
        console.error('Error:', error);
        typingMessage.remove();

        const errorMsg = currentLanguage === 'ckb' ?
            'ببوورە، کێشەم هەیە لە پەیوەندیکردن. تکایە دووبارە هەوڵ بدەوە.' :
            "I'm sorry, I'm having trouble connecting right now. Please try again later.";
        addMessage(errorMsg, false);
    }
}

// Translation function using Google Translate API (free tier)
async function translateText(text, fromLang, toLang) {
    try {
        // Using a free translation service
        const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${fromLang}|${toLang}`);
        const data = await response.json();

        if (data.responseStatus === 200) {
            return data.responseData.translatedText;
        } else {
            return text; // Return original text if translation fails
        }
    } catch (error) {
        console.error('Translation error:', error);
        return text; // Return original text if translation fails
    }
}

// Event listeners
document.getElementById('user-message').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

document.getElementById('clear-chat').addEventListener('click', clearChat);

function scrollToBottom() {
    const chatContainer = document.getElementById('chat-messages');
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Initialize the chat
document.addEventListener('DOMContentLoaded', function() {
    generateSuggestedQuestions();
});
</script>
</div>