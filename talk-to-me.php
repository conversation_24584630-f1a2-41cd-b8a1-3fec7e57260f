<div class="container centered">

    <div id="chat-container">
        <div id="chat-messages"></div>
        <div id="user-input">
            <input type="text" id="user-message" placeholder="Type your message...">
            <button id="send-btn" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
    const questions = [
        "What is depression?",
        "How to cope with anxiety?",
        "Can you explain bipolar disorder?",
        "What are the symptoms of PTSD?",
        "Tell me about schizophrenia.",
        "What is social anxiety?",
        "How to deal with panic attacks?",
        "What is obsessive-compulsive disorder (OCD)?",
        "Tell me about eating disorders.",
        "How to manage stress?",
        "What are the signs of attention-deficit/hyperactivity disorder (ADHD)?",
        "How does postpartum depression affect new mothers?",
        "Can you explain borderline personality disorder (BPD)?",
        "Tell me about seasonal affective disorder (SAD).",
        "How to overcome phobias?",

        "What is generalized anxiety disorder (GAD)?",
        "How is major depressive disorder (MDD) diagnosed?",
        "What are the common symptoms of post-traumatic stress disorder (PTSD)?",
        "Can you explain the characteristics of obsessive-compulsive disorder (OCD)?",
        "How does bipolar disorder impact a person's mood?",
        "What is the connection between trauma and dissociative identity disorder (DID)?",
        "What are the signs of attention-deficit/hyperactivity disorder (ADHD) in adults?",
        "Can you describe the symptoms of social anxiety disorder?",
        "How is schizophrenia treated?",
        "What is the relationship between eating disorders and body image?",
        "How does borderline personality disorder (BPD) affect relationships?",
        "What are the risk factors for developing panic disorder?",
        "How is insomnia typically addressed in therapy?",
        "Can you explain the concept of seasonal affective disorder (SAD)?",
        "What is the difference between agoraphobia and social anxiety?",
        "How is specific phobia typically treated?",
        "What are the effects of long-term stress on mental health?",
        "How does narcissistic personality disorder (NPD) manifest in relationships?",
        "Can you describe the symptoms of avoidant personality disorder?",
        "How is hoarding disorder different from general clutter?"
    ];

    const treatments = [
        "Treatment for depression may include therapy, medication, and lifestyle changes.",
        "Coping with anxiety involves relaxation techniques, therapy, and stress management.",
        "Bipolar disorder treatment often includes mood stabilizers and therapy.",
        "PTSD treatment may involve therapy, medication, and support groups.",
        "Schizophrenia treatment includes antipsychotic medications and therapy.",
        "Social anxiety can be managed with therapy, medication, and exposure therapy.",
        "Panic attacks can be treated with therapy and sometimes medication.",
        "OCD treatment includes therapy, medication, and exposure and response prevention.",
        "Eating disorders may require therapy, nutritional counseling, and medical monitoring.",
        "Managing stress involves self-care, relaxation techniques, and seeking support.",
        "ADHD treatment may include medication, therapy, and behavioral interventions.",
        "Postpartum depression can be treated with therapy and sometimes medication.",
        "BPD treatment involves therapy, medication, and support from loved ones.",
        "SAD treatment may include light therapy, medication, and lifestyle changes.",
        "Phobias can be addressed through exposure therapy and cognitive-behavioral therapy.",

        "Treatment often involves cognitive-behavioral therapy (CBT), medication (antidepressants or benzodiazepines), and relaxation techniques to manage excessive worry and anxiety.",
        "Treatments include psychotherapy (especially cognitive-behavioral therapy), antidepressant medications, and in severe cases, electroconvulsive therapy (ECT).",
        "Therapies like cognitive processing therapy (CPT), exposure therapy, and eye movement desensitization and reprocessing (EMDR) are commonly used. Medications may also be prescribed.",
        "Treatment includes exposure and response prevention (ERP) therapy, cognitive-behavioral therapy (CBT), and medication, such as selective serotonin reuptake inhibitors (SSRIs).",
        "Medications like mood stabilizers (e.g., lithium) and antipsychotics, along with psychotherapy (especially psychoeducation and cognitive-behavioral therapy), are common treatments.",
        "Psychotherapy, particularly dialectical behavior therapy (DBT) and cognitive-behavioral therapy, is often used to help integrate dissociated identities and manage symptoms.",
        "Treatment includes a combination of behavioral interventions, psychoeducation, and medications like stimulants (e.g., Adderall) or non-stimulants (e.g., Strattera).",
        "Cognitive-behavioral therapy (CBT), exposure therapy, and medications such as selective serotonin reuptake inhibitors (SSRIs) are common interventions.",
        "Treatment typically involves antipsychotic medications, psychoeducation, and psychosocial interventions like cognitive-behavioral therapy for psychosis (CBTp).",
        "Treatment involves a multidisciplinary approach, including psychotherapy (CBT, dialectical behavior therapy), nutritional counseling, and medical monitoring.",
        "Dialectical behavior therapy (DBT), mentalization-based therapy (MBT), and psychodynamic psychotherapy are often used to address emotional dysregulation and interpersonal difficulties.",
        "Cognitive-behavioral therapy (CBT), particularly exposure therapy and panic-focused psychodynamic psychotherapy, along with medications like SSRIs or benzodiazepines.",
        "Cognitive-behavioral therapy for insomnia (CBT-I) is a primary treatment, focusing on improving sleep hygiene, addressing negative thoughts about sleep, and establishing a consistent sleep routine.",
        "Light therapy (exposure to a bright light that mimics natural sunlight), psychotherapy, and medication (antidepressants) are common treatments.",
        "Cognitive-behavioral therapy (CBT), exposure therapy, and medications like SSRIs are often used to manage symptoms and improve functioning in social and public settings.",
        "Exposure therapy, cognitive-behavioral therapy (CBT), and desensitization techniques are effective interventions for specific phobias.",
        "Stress management techniques, mindfulness-based interventions, psychotherapy, and lifestyle changes are important for mitigating the impact of long-term stress on mental health.",
        "Treatment often involves individual therapy to address underlying insecurities and interpersonal difficulties, although individuals with NPD may be resistant to seeking help.",
        "Cognitive-behavioral therapy (CBT) and psychodynamic therapy are commonly used to help individuals with avoidant personality disorder improve social functioning and self-esteem.",
        "Treatment includes cognitive-behavioral therapy (CBT) with a focus on exposure and response prevention, support groups, and sometimes medication to address symptoms of anxiety or depression associated with hoarding."
    ];

    let chatMessages = document.getElementById('chat-messages');
    let currentQuestionIndex = 0;

    function getRandomQuestions() {
        const randomQuestions = [];
        while (randomQuestions.length < 5) {
            const randomIndex = Math.floor(Math.random() * questions.length);
            if (!randomQuestions.includes(randomIndex)) {
                randomQuestions.push(randomIndex);
            }
        }
        return randomQuestions;
    }

    function generateQuestions() {
        const randomQuestions = getRandomQuestions();
        randomQuestions.forEach((questionIndex) => {
            chatMessages.innerHTML +=
                `<div class="message" onclick="startChat(${questionIndex})">${questions[questionIndex]}</div>`;
        });
    }

    function startChat(questionIndex) {
        currentQuestionIndex = questionIndex;
        chatMessages.innerHTML += `<div class="message bot-message">${treatments[questionIndex]}</div>`;
    }

    function sendMessage() {
        const userMessage = document.getElementById('user-message').value;
        if (userMessage.trim() !== "") {
            chatMessages.innerHTML += `<div class="message user-message">User: ${userMessage}</div>`;
            document.getElementById('user-message').value = "";
            respondToUser(userMessage);
        }
    }

    async function respondToUser(userMessage) {
        // Show typing indicator
        chatMessages.innerHTML +=
            `<div class="message bot-message typing" id="typing-indicator">Bot is typing...</div>`;
        scrollToBottom();

        try {
            // Send message to Gemini API via PHP backend
            const response = await fetch('gemini_chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: userMessage
                })
            });

            const data = await response.json();

            // Remove typing indicator
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }

            if (data.success) {
                chatMessages.innerHTML += `<div class="message bot-message">Bot: ${data.response}</div>`;
            } else {
                chatMessages.innerHTML +=
                    `<div class="message bot-message">Bot: I'm sorry, I'm having trouble responding right now. Please try again later.</div>`;
            }
        } catch (error) {
            console.error('Error:', error);
            // Remove typing indicator
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
            chatMessages.innerHTML +=
                `<div class="message bot-message">Bot: I'm sorry, I'm having trouble connecting right now. Please try again later.</div>`;
        }

        scrollToBottom();
    }

    generateQuestions();

    document.getElementById('user-message').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    function scrollToBottom() {
        const chatContainer = document.getElementById('chat-messages');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    function sendMessage() {
        const userMessage = document.getElementById('user-message').value;
        if (userMessage.trim() !== "") {
            chatMessages.innerHTML += `<div class="message user-message">User: ${userMessage}</div>`;
            document.getElementById('user-message').value = "";
            respondToUser(userMessage);
            scrollToBottom(); // Scroll down after sending a message
        }
    }

    function startChat(questionIndex) {
        currentQuestionIndex = questionIndex;
        chatMessages.innerHTML += `<div class="message bot-message">${treatments[questionIndex]}</div>`;
        scrollToBottom(); // Scroll down after receiving a message
    }
    </script>
</div>