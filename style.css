@font-face {
  font-family: Rabar;
  src: url(font/Rabar_22.ttf);
}
@font-face {
  font-family: RabarBold;
  src: url(font/Rabar_21.ttf);
}
* {
  font-family: Rabar !important;
}
html {
  scrollbar-gutter: stable !important;
}
body {
  background-color: #d5f6ff !important;
}
.text-teal {
  color: #265e6d !important;
}
.bg-teal {
  background-color: #265e6d !important;
}
.border-teal {
  border: 3px solid #265e6d !important;
}
.button-teal {
  background-color: #265e6d !important;
  border: none !important;
  outline: none !important;
}
.button-teal:hover {
  background-color: #10af9c !important;
  border: none !important;
  outline: none !important;
}
.no-outline {
  border: none !important;
  outline: none !important;
}
.modal-content {
  border-radius: 0 !important;
  border: none !important;
  outline: none !important;
  background-color: #d5f6ff !important;
}
nav {
  background-color: #265e6d !important;
  font-family: RabarBold !important;
  font-size: large !important;
  width: 100%;
  position: fixed !important;
  top: 0 !important;
  z-index: 1000;
}
nav a p {
  color: whitesmoke !important;
}
ul {
  padding-inline-start: 0 !important;
}
ul li a {
  color: whitesmoke !important;
}
.centered {
  display: flex;
  justify-content: center;
  align-items: center;
}
.container {
  padding-top: 100px !important;
}
.team-member p {
  text-indent: 25px;
}
.inLess {
  text-indent: 0 !important;
  font-family: RabarBold !important;
}
.mainl,
.awarei {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}
.mainlist {
  width: 100% !important;
  position: fixed !important;
  bottom: 0 !important;
}
.textTalk {
  transform: rotate(180deg);
}
.talkBox {
  display: none;
  width: auto;
  height: auto;
  text-align: center;
  background: #ddd;
  padding: 5px;
  margin: 35px auto;
  position: relative;
  border-radius: 10px;
  position: fixed;
  transform: rotate(180deg);
  bottom: 4em;
}
.talkBox:before {
  content: "";
  display: block;
  width: 0;
  border-width: 15px 15px 0px 15px;
  border-color: #ddd transparent;
  border-style: solid;
  position: absolute;
  bottom: -15px;
  right: 50%;
  transform: translateX(50%);
}
.active {
  background-color: rgba(16, 175, 156, 0.75) !important;
  height: 70px !important;
  padding: 0 !important;
  margin: 0 !important;
}
.nav-item:hover,
.active:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: 70px !important;
  margin: 0 !important;
}
.navbar {
  padding: 0;
  margin: 0;
  border: 0;
  display: flex;
  align-items: center;
}
.nav-link {
  height: 70px !important;
  padding: 0 10px !important;
  margin: 0 !important;
  font-size: medium;
}
.nav-item {
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 720px) {
  .nav-item,
  .active,
  .nav-link,
  .nav-item:hover,
  .active:hover {
    height: 2em !important;
    width: 100%;
  }
}
h1 {
  font-size: 5vw;
}
h2 {
  font-size: 3.5vw;
}
@media screen and (max-width: 1300px) {
  h1 {
    font-size: 9vw;
  }
  h2 {
    font-size: 5vw;
  }
}
@media screen and (max-width: 600px) {
  h1 {
    font-size: 15vw;
  }
  h2 {
    font-size: 10.5vw;
  }
}
.nave-item {
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.nave-item:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.hover-red:hover {
  background-color: rgba(175, 16, 16, 0.5) !important;
}
#popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 30px;
  background-color: #f9f9f9;
  border: 2px solid #ccc;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  text-align: center;
  width: 100%;
  height: 100vh;
}

#emoji-container {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

@media screen and (max-width: 600px) {
  #popup {
    padding-top: 18em;
  }
}

.emoji {
  cursor: pointer;
  font-size: 15vw;
  margin: 0 10px;
}

#popup p {
  font-size: 10vw;
  margin-bottom: 20px;
}

/*----------AWARENESS----------*/
.awareBox {
  height: 100% !important;
}
.awareMiniBox {
  height: 100% !important;
}
.aware {
  display: flex;
  justify-content: center;
  align-items: end;
}
.awarei {
  width: 66%;
  position: relative;
}
.awareLabel {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: larger;
  background-color: rgba(46, 133, 98, 0.75);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel:hover {
  scale: 1.2;
}
.awareLabel2 {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: x-large;
  background-color: rgba(46, 133, 98, 0.9);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel2:hover {
  scale: 1.2;
}

.imgP img {
  width: 75%;
  display: flex;
  justify-content: center;
  align-items: start;
}
@media screen and (max-width: 600px) {
  .imgP img {
    width: 100%;
  }
}
.textBG {
  background-color: #ffffff84;
}
.textBG p {
  text-indent: 25px;
}
.backBtn {
  cursor: pointer;
  position: fixed;
  bottom: 0;
  right: 0;
  color: #ed7014;
}
.textBox {
  background-color: #f8f9fa;
  width: auto;
  height: 100px;
}

/*----------TEST----------*/
.testLabel {
  text-decoration: none;
  color: #ed7014;
  font-size: x-large;
}

/*----------FIND CENTER----------*/
.gMapsText {
  background-color: rgba(0, 0, 0, 0.75);
  color: #f8f9fa;
}

/*----------TALK TO ME----------*/
#chat-container {
  width: 90%;
  margin: 50px auto;
  border: 1px solid #ccc;
  border-radius: 5px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#chat-messages {
  height: 50vh;
  overflow-y: auto;
  padding: 10px;
}

#user-input {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
}

#user-input input {
  flex: 1;
  padding: 8px;
  margin-right: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

#send-btn {
  cursor: pointer;
  padding: 8px;
  border: none;
  border-radius: 3px;
  background-color: #ed7014;
  color: #fff;
}

.message {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ed7014;
  cursor: pointer;
}

.user-message {
  background-color: #ed7014;
  color: #fff;
}

.bot-message {
  background-color: #4caf50;
  color: #fff;
}

.typing {
  opacity: 0.7;
  font-style: italic;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/*----------ONLINE CONTACT----------*/
.contact-card {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 1em;
  height: 1em;
  background-color: #4caf50;
  border-radius: 50%;
  border: 2px solid #fff;
}
.offline {
  background-color: #f44336; /* Red color for offline status */
}
.profile-section {
  padding: 20px;
}

.profile-section img {
  border-radius: 50%;
  width: 120px;
  height: 120px;
  object-fit: cover;
  margin-bottom: 10px;
}

#name {
  margin: 0;
  color: #333;
}

#email,
#phone {
  color: #666;
  margin: 5px 0;
}

.social-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
}

.social-icons a {
  margin: 0 10px;
  text-decoration: none;
  color: #333;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: #007bff;
}

.social-icons img {
  width: 30px;
  height: 30px;
}

/*----------ABOUT----------*/
.containers {
  position: relative;
  z-index: 1;
  background-color: #ffffff6c;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("background-image.jpg");
  background-size: cover;
  background-position: center;
  filter: blur(5px);
  z-index: 0;
}

header {
  text-align: center;
  padding: 20px;
}

.team-members {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.team-member {
  flex: 0 1 calc(45% - 20px);
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #f9f9f9;
  overflow: hidden;
}

.team-member img {
  width: 200px;
  border-radius: 50%;
}

.about-content {
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #f9f9f9;
}

footer {
  background-color: #333;
  color: #fff;
  text-align: center;
  padding: 10px;
  position: static;
  bottom: 0;
  width: 100%;
}

/* Responsive adjustments */
@media only screen and (max-width: 768px) {
  .team-member {
    flex: 1 1 calc(100% - 20px);
  }
}

/*---------- TRANSLATE ----------*/

.rtl {
  direction: rtl; /* Set the direction to right-to-left */
}
.ltr {
  direction: ltr; /* Set the direction to right-to-left */
}

.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem auto -1rem -1rem;
}

.custom-file-upload input[type="file"] {
  display: none;
}

/*---------- CARD UPDATE ----------*/

.profile-image-preview {
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 50%;
  display: inline-block;
}
.profile-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Crops the image to fit the square */
}

.time-slot-group {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

#scheduleContent ul,
#weekScheduleBody div {
  direction: ltr !important;
  text-align: right !important;
  list-style-type: none;
}

/* Credit Options */
.add {
  width: 2rem;
  height: 2rem;
  font-size: 1.5rem;
  background-color: #ffffff;
  color: #265e6d !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.add:hover {
  background-color: #10af9c;
  color: #ffffff !important;
}
.credit-options {
  display: flex;
  justify-content: space-around;
  gap: 5px;
}
.credit-card {
  flex: 1;
  display: inline-block;
  cursor: pointer;
  position: relative;
  text-align: center;
  background-color: transparent;
  border: 2px solid transparent;
  border-radius: 1rem;
}
.credit-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.credit-card input[type="radio"]:checked + .credit-card-content {
  border: 2px solid #ed7014;
  padding: 20px 10px;
  border-radius: 1rem;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.credit-card-content {
  background-color: #ffffff;
  padding: 20px 10px;
  border-radius: 1rem;
}
.credit-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 10px;
}
.credit-card h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #265e6d;
  margin-bottom: 15px;
}
.credit-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.credit-card ul li {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

/* Recommended style */
.credit-card.recommended h2 {
  color: #ed7014;
}
/* Payment Methods */
.payment-methods {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20px;
}
.payment-method {
  cursor: pointer;
  text-align: center;
  border: 2px solid transparent;
  padding: 10px;
}
.payment-method input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.payment-method input[type="radio"]:checked + img {
  border: 2px solid #ed7014;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.payment-method img {
  height: 40px;
  margin-bottom: 5px;
}
.payment-method span {
  font-weight: 600;
  color: #333;
}
