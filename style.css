@font-face {
  font-family: Rabar;
  src: url(font/Rabar_22.ttf);
}
@font-face {
  font-family: RabarBold;
  src: url(font/Rabar_21.ttf);
}
* {
  font-family: Rabar !important;
}
html {
  scrollbar-gutter: stable !important;
}
body {
  background-color: #d5f6ff !important;
}
.text-teal {
  color: #265e6d !important;
}
.bg-teal {
  background-color: #265e6d !important;
}
.border-teal {
  border: 3px solid #265e6d !important;
}
.button-teal {
  background-color: #265e6d !important;
  border: none !important;
  outline: none !important;
}
.button-teal:hover {
  background-color: #10af9c !important;
  border: none !important;
  outline: none !important;
}
.no-outline {
  border: none !important;
  outline: none !important;
}
.modal-content {
  border-radius: 0 !important;
  border: none !important;
  outline: none !important;
  background-color: #d5f6ff !important;
}
nav {
  background-color: #265e6d !important;
  font-family: RabarBold !important;
  font-size: large !important;
  width: 100%;
  position: fixed !important;
  top: 0 !important;
  z-index: 1000;
}
nav a p {
  color: whitesmoke !important;
}
ul {
  padding-inline-start: 0 !important;
}
ul li a {
  color: whitesmoke !important;
}
.centered {
  display: flex;
  justify-content: center;
  align-items: center;
}
.container {
  padding-top: 100px !important;
}
.team-member p {
  text-indent: 25px;
}
.inLess {
  text-indent: 0 !important;
  font-family: RabarBold !important;
}
.mainl,
.awarei {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}
.mainlist {
  width: 100% !important;
  position: fixed !important;
  bottom: 0 !important;
}
.textTalk {
  transform: rotate(180deg);
}
.talkBox {
  display: none;
  width: auto;
  height: auto;
  text-align: center;
  background: #ddd;
  padding: 5px;
  margin: 35px auto;
  position: relative;
  border-radius: 10px;
  position: fixed;
  transform: rotate(180deg);
  bottom: 4em;
}
.talkBox:before {
  content: "";
  display: block;
  width: 0;
  border-width: 15px 15px 0px 15px;
  border-color: #ddd transparent;
  border-style: solid;
  position: absolute;
  bottom: -15px;
  right: 50%;
  transform: translateX(50%);
}
.active {
  background-color: rgba(16, 175, 156, 0.75) !important;
  height: 70px !important;
  padding: 0 !important;
  margin: 0 !important;
}
.nav-item:hover,
.active:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: 70px !important;
  margin: 0 !important;
}
.navbar {
  padding: 0;
  margin: 0;
  border: 0;
  display: flex;
  align-items: center;
}
.nav-link {
  height: 70px !important;
  padding: 0 10px !important;
  margin: 0 !important;
  font-size: medium;
}
.nav-item {
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 720px) {
  .nav-item,
  .active,
  .nav-link,
  .nav-item:hover,
  .active:hover {
    height: 2em !important;
    width: 100%;
  }
}
h1 {
  font-size: 5vw;
}
h2 {
  font-size: 3.5vw;
}
@media screen and (max-width: 1300px) {
  h1 {
    font-size: 9vw;
  }
  h2 {
    font-size: 5vw;
  }
}
@media screen and (max-width: 600px) {
  h1 {
    font-size: 15vw;
  }
  h2 {
    font-size: 10.5vw;
  }
}
.nave-item {
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.nave-item:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.hover-red:hover {
  background-color: rgba(175, 16, 16, 0.5) !important;
}
#popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 30px;
  background-color: #f9f9f9;
  border: 2px solid #ccc;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  text-align: center;
  width: 100%;
  height: 100vh;
}

#emoji-container {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

@media screen and (max-width: 600px) {
  #popup {
    padding-top: 18em;
  }
}

.emoji {
  cursor: pointer;
  font-size: 15vw;
  margin: 0 10px;
}

#popup p {
  font-size: 10vw;
  margin-bottom: 20px;
}

/*----------AWARENESS----------*/
.awareBox {
  height: 100% !important;
}
.awareMiniBox {
  height: 100% !important;
}
.aware {
  display: flex;
  justify-content: center;
  align-items: end;
}
.awarei {
  width: 66%;
  position: relative;
}

/* Apply hue filter to bot icons */
.awarei[src*="bot/"] {
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
}
.awareLabel {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: larger;
  background-color: rgba(46, 133, 98, 0.75);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel:hover {
  scale: 1.2;
}
.awareLabel2 {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: x-large;
  background-color: rgba(46, 133, 98, 0.9);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel2:hover {
  scale: 1.2;
}

.imgP img {
  width: 75%;
  display: flex;
  justify-content: center;
  align-items: start;
}
@media screen and (max-width: 600px) {
  .imgP img {
    width: 100%;
  }
}
.textBG {
  background-color: #ffffff84;
}
.textBG p {
  text-indent: 25px;
}
.backBtn {
  cursor: pointer;
  position: fixed;
  bottom: 0;
  right: 0;
  color: #ed7014;
}
.textBox {
  background-color: #f8f9fa;
  width: auto;
  height: 100px;
}

/*----------TEST----------*/
.testLabel {
  text-decoration: none;
  color: #ed7014;
  font-size: x-large;
}

/*----------FIND CENTER----------*/
.gMapsText {
  background-color: rgba(0, 0, 0, 0.75);
  color: #f8f9fa;
}

/*----------MODERN CHAT INTERFACE----------*/
.modern-chat-wrapper {
  max-width: 800px;
  margin: 120px auto 20px auto;
  padding: 0 15px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

.language-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.lang-btn {
  padding: 8px 20px;
  border: 2px solid #ed7014;
  background: transparent;
  color: #ed7014;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.lang-btn.active,
.lang-btn:hover {
  background: #ed7014;
  color: white;
}

.modern-chat-container {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e1e5e9;
  height: 75vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: linear-gradient(135deg, #ed7014 0%, #f39c12 100%);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}

.bot-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.bot-avatar img {
  width: 35px;
  height: 35px;
  object-fit: contain;
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
  transition: all 0.3s ease;
}

.bot-avatar img:hover {
  transform: scale(1.1);
}

.avatar-fallback {
  font-size: 24px;
  display: none;
}

.bot-info {
  flex: 1;
}

.bot-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.online-status {
  font-size: 12px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 5px;
}

.online-status::before {
  content: "●";
  color: #4caf50;
  font-size: 10px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modern-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 0;
}

.welcome-message {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.bot-avatar-small {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: rgba(237, 112, 20, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
  overflow: hidden;
}

.bot-avatar-small img {
  width: 25px;
  height: 25px;
  object-fit: contain;
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
}

.message-content {
  flex: 1;
}

.message-content p {
  background: white;
  padding: 15px;
  border-radius: 15px;
  margin: 0 0 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.suggested-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question {
  background: #ed7014;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s ease;
}

.suggested-question:hover {
  background: #d35400;
  transform: translateY(-1px);
}
.chat-message {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
  animation: fadeInUp 0.3s ease;
}

.chat-message.user-message {
  flex-direction: row-reverse;
}

.chat-message.user-message .message-bubble {
  background: #ed7014;
  color: white;
}

.chat-message.bot-message .message-bubble {
  background: white;
  color: #333;
  border: 1px solid #e1e5e9;
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-content {
  margin-bottom: 5px;
  line-height: 1.4;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
}

.typing .message-bubble {
  background: #f0f0f0;
  color: #666;
  font-style: italic;
}

.typing .message-content::after {
  content: "...";
  animation: typingDots 1.5s infinite;
}

@keyframes typingDots {
  0%,
  20% {
    content: "...";
  }
  40% {
    content: "....";
  }
  60% {
    content: ".....";
  }
  80%,
  100% {
    content: "...";
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-chat-input {
  background: white;
  padding: 20px;
  border-top: 1px solid #e1e5e9;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 25px;
  outline: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.input-container input:focus {
  border-color: #ed7014;
  box-shadow: 0 0 0 3px rgba(237, 112, 20, 0.1);
}

.send-button {
  width: 45px;
  height: 45px;
  border: none;
  background: #ed7014;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover {
  background: #d35400;
  transform: scale(1.05);
}

.input-footer {
  text-align: center;
  margin-top: 10px;
}

.input-footer small {
  color: #666;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-chat-wrapper {
    margin: 100px 10px 10px 10px;
    padding: 0;
  }

  .modern-chat-container {
    height: 85vh;
    max-height: 600px;
  }

  .message-bubble {
    max-width: 85%;
  }

  .chat-header {
    padding: 15px;
  }

  .bot-info h3 {
    font-size: 16px;
  }

  .modern-chat-input {
    padding: 15px;
  }
}

/* RTL Support for Kurdish */
[dir="rtl"] .chat-message.user-message {
  flex-direction: row;
}

[dir="rtl"] .chat-message.bot-message {
  flex-direction: row-reverse;
}

[dir="rtl"] .message-time {
  text-align: left;
}

[dir="rtl"] .input-container {
  direction: rtl;
}

[dir="rtl"] .suggested-questions {
  direction: rtl;
}

/*----------ONLINE CONTACT----------*/
.contact-card {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 1em;
  height: 1em;
  background-color: #4caf50;
  border-radius: 50%;
  border: 2px solid #fff;
}
.offline {
  background-color: #f44336; /* Red color for offline status */
}
.profile-section {
  padding: 20px;
}

.profile-section img {
  border-radius: 50%;
  width: 120px;
  height: 120px;
  object-fit: cover;
  margin-bottom: 10px;
}

#name {
  margin: 0;
  color: #333;
}

#email,
#phone {
  color: #666;
  margin: 5px 0;
}

.social-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
}

.social-icons a {
  margin: 0 10px;
  text-decoration: none;
  color: #333;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: #007bff;
}

.social-icons img {
  width: 30px;
  height: 30px;
}

/*----------ABOUT----------*/
.containers {
  position: relative;
  z-index: 1;
  background-color: #ffffff6c;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("background-image.jpg");
  background-size: cover;
  background-position: center;
  filter: blur(5px);
  z-index: 0;
}

header {
  text-align: center;
  padding: 20px;
}

.team-members {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.team-member {
  flex: 0 1 calc(45% - 20px);
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #f9f9f9;
  overflow: hidden;
}

.team-member img {
  width: 200px;
  border-radius: 50%;
}

.about-content {
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #f9f9f9;
}

footer {
  background-color: #333;
  color: #fff;
  text-align: center;
  padding: 10px;
  position: static;
  bottom: 0;
  width: 100%;
}

/* Responsive adjustments */
@media only screen and (max-width: 768px) {
  .team-member {
    flex: 1 1 calc(100% - 20px);
  }
}

/*---------- TRANSLATE ----------*/

.rtl {
  direction: rtl; /* Set the direction to right-to-left */
}
.ltr {
  direction: ltr; /* Set the direction to right-to-left */
}

.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem auto -1rem -1rem;
}

.custom-file-upload input[type="file"] {
  display: none;
}

/*---------- CARD UPDATE ----------*/

.profile-image-preview {
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 50%;
  display: inline-block;
}
.profile-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Crops the image to fit the square */
}

.time-slot-group {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

#scheduleContent ul,
#weekScheduleBody div {
  direction: ltr !important;
  text-align: right !important;
  list-style-type: none;
}

/* Credit Options */
.add {
  width: 2rem;
  height: 2rem;
  font-size: 1.5rem;
  background-color: #ffffff;
  color: #265e6d !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.add:hover {
  background-color: #10af9c;
  color: #ffffff !important;
}
.credit-options {
  display: flex;
  justify-content: space-around;
  gap: 5px;
}
.credit-card {
  flex: 1;
  display: inline-block;
  cursor: pointer;
  position: relative;
  text-align: center;
  background-color: transparent;
  border: 2px solid transparent;
  border-radius: 1rem;
}
.credit-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.credit-card input[type="radio"]:checked + .credit-card-content {
  border: 2px solid #ed7014;
  padding: 20px 10px;
  border-radius: 1rem;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.credit-card-content {
  background-color: #ffffff;
  padding: 20px 10px;
  border-radius: 1rem;
}
.credit-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 10px;
}
.credit-card h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #265e6d;
  margin-bottom: 15px;
}
.credit-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.credit-card ul li {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

/* Recommended style */
.credit-card.recommended h2 {
  color: #ed7014;
}
/* Payment Methods */
.payment-methods {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20px;
}
.payment-method {
  cursor: pointer;
  text-align: center;
  border: 2px solid transparent;
  padding: 10px;
}
.payment-method input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.payment-method input[type="radio"]:checked + img {
  border: 2px solid #ed7014;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.payment-method img {
  height: 40px;
  margin-bottom: 5px;
}
.payment-method span {
  font-weight: 600;
  color: #333;
}
