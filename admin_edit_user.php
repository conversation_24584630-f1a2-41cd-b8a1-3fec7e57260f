<?php
session_start();
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Get user ID from URL
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch user data from the database
$query = "SELECT id, username, email, role FROM users WHERE id = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    echo "<div class='alert alert-danger'>User not found.</div>";
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $email = $_POST['email'];
    $role = $_POST['role'];

    // Update user details in the database
    $update_query = "UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?";
    $update_stmt = $con->prepare($update_query);
    $update_stmt->bind_param("sssi", $username, $email, $role, $user_id);

    if ($update_stmt->execute()) {
        echo "<div class='alert alert-success'>User updated successfully.</div>";
    } else {
        echo "<div class='alert alert-danger'>Failed to update user.</div>";
    }

    // Refresh the page to reflect changes
    header("Location: admin_users");
    exit();
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>Edit User</title>
    <?php include 'head.php'; ?>
</head>
<body>
<?php include 'admin_navbar.php'; ?>

<div class="container mt-5">
    <h1>Edit User</h1>

    <form method="POST" action="">
        <div class="mb-3">
            <label for="username" class="form-label">Username</label>
            <input type="text" class="form-control" id="username" name="username" value="<?php echo $user['username']; ?>" required>
        </div>
        <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email']; ?>" required>
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">Role</label>
            <select class="form-control" id="role" name="role" required>
                <option value="Admin" <?php echo ($user['role'] == 'Admin') ? 'selected' : ''; ?>>Admin</option>
                <option value="User" <?php echo ($user['role'] == 'User') ? 'selected' : ''; ?>>User</option>
                <option value="Doctor" <?php echo ($user['role'] == 'Doctor') ? 'selected' : ''; ?>>Doctor</option>
                <option value="Psychologist" <?php echo ($user['role'] == 'Psychologist') ? 'selected' : ''; ?>>Psychologist</option>
                <option value="Counsellor" <?php echo ($user['role'] == 'Counsellor') ? 'selected' : ''; ?>>Counsellor</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Update User</button>
        <a href="admin_users" class="btn btn-secondary">Cancel</a>
    </form>
</div>

<?php include 'script.php'; ?>
</body>
</html>
