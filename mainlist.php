<div class="main-box h-100" style="padding-top: 100px;">
    <div class="text-center d-block d-sm-none" style="color: #265e6d">
        <h1 style="font-family: RabarBold !important;">میهرەبان بە</h1>
        <h2 style="font-family: Ra<PERSON> !important;">لەگەڵ دەروونت!</h2>
    </div>
    <div class="text-center d-none d-sm-block" style="color: #265e6d">
        <h1 class="d-inline" style="font-family: RabarBold !important;">میهرەبان بە </h1>
        <h2 class="d-inline" style="font-family: Rabar !important;">لەگەڵ دەروونت!</h2>
    </div>
    <div class="mainlist d-flex justify-content-center">
        <img class="mainl" src="img/mainlist.png" usemap="#image-map">
        <map name="image-map">
            <area id="area1" class="areaa" alt="هۆشیاری" title="هۆشیاری" href="awarenesses" coords="111,42,43" shape="circle">
            <area id="area2" class="areaa" alt="ڕاوێژکاری" title="ڕاوێژکاری" href="counseling" coords="238,42,42" shape="circle">
            <area id="area3" class="areaa" alt="ڕێنمایی قوتابخانە" title="ڕێنمایی قوتابخانە" href="school-guidance" coords="42,121,42" shape="circle">
            <area id="area4" class="areaa" alt="سەنتەری دەروونی" title="سەنتەری دەروونی" href="find-center" coords="307,117,42" shape="circle">
            <area id="area5" class="areaa" alt="تێستی دەروونی" title="تێستی دەروونی" href="online-test" coords="302,221,42" shape="circle">
            <area id="area6" class="areaa" alt="دەربارەی ئێمە" title="دەربارەی ئێمە" href="about" coords="48,221,41" shape="circle">
            <area id="area7" class="areaa" alt="سەرەکی" title="سەرەکی" href="home" coords="175,126,22" shape="circle">
        </map>
        <div class="talkBox">
            <h6 class="textTalk"></h6>
        </div>
    </div>
</div>
<script>
    function showTooltip(event) {
  const tooltip = document.querySelector(".talkBox");
  tooltip.style.display = "block";
  const title = event.target.getAttribute("title");
  tooltip.querySelector(".textTalk").innerText = title;
}

function hideTooltip() {
  const tooltip = document.querySelector(".talkBox");
  tooltip.querySelector(".textTalk").innerText = "";
  tooltip.style.display = "none";
}

// Attach event listeners to each area
for (let i = 1; i <= 7; i++) {
  const area = document.querySelector(`#area${i}`);
  area.addEventListener("mouseover", showTooltip);
  area.addEventListener("mouseout", hideTooltip);
}
</script>