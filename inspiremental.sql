-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 29, 2025 at 09:48 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `inspiremental`
--

-- --------------------------------------------------------

--
-- Table structure for table `purchases`
--

CREATE TABLE `purchases` (
  `id` int(11) <PERSON>SIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `purchase_amount` int(11) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_status` enum('pending','completed','failed') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `transaction_type` enum('purchase','usage') DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tests`
--

CREATE TABLE `tests` (
  `id` int(11) NOT NULL,
  `test_name` varchar(255) NOT NULL,
  `test_name_kurdish` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `questions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`questions`)),
  `scoring_info` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tests`
--

INSERT INTO `tests` (`id`, `test_name`, `test_name_kurdish`, `description`, `questions`, `scoring_info`, `created_at`, `updated_at`) VALUES
(1, 'Mental Health Today', 'تەندروستی دەروونیت ئەمڕۆ', 'هەڵسەنگاندنی باری دەروونی ئێستا', '[\"کێشەم هەیە لە سەرنجدان لە شتەکان وەک خوێندنەوە، تەلەفیزیۆن، یان گفتوگۆ.\",\"زۆرجار هەست بە بێئارامی یان نائاسوودەیی دەکەم بەبێ ئەوەی هۆکارەکەی بزانم.\",\"هەستی لەناکاو بە ترس یان تۆقینم هەبووە.\",\"دوور دەکەومەوە لە شوێن یان دۆخە دیاریکراوەکان چونکە دڵەڕاوکێم دەکەن.\",\"خۆم دەبینمەوە کە ڕووداوە کارەساتبارەکانی ڕابردوو لە مێشکمدا دووبارە دەکەمەوە.\",\"هەست دەکەم لە خەڵکی دەوروبەرم جیا بوومەتەوە.\",\"تەقەڵا دەکەم لە کۆنترۆڵکردنی هەستی تووڕەیی یان پەشۆکاوی.\",\"زۆرجار کێشەم هەیە لە خەوتن یان لە خەو مانەوە.\",\"بەردەوام هەست بە گرژی یان زۆر ئاگاداری دەوروبەرم دەکەم.\",\"کێشەم هەیە لە متمانەکردن بە کەسانی تر.\"]', 'نمرە ٠-١٠: نیگەرانی کەم، ١١-٢٥: نیگەرانی مامناوەند، ٢٦-٤٠: نیگەرانی زۆر - پێویستە یارمەتی پسپۆڕانە بخوازیت', NOW(), NOW()),
(2, 'Depression Test', 'خەمۆکی', 'هەڵسەنگاندن بۆ نیشانەکانی خەمۆکی', '[\"زۆربەی کات هەست بە خەمۆکی یان بەتاڵی دەکەم.\",\"حەزم لە چالاکییەکان نەماوە کە پێشتر چێژم لێ دەبینی.\",\"زۆربەی ڕۆژەکان هەست بە ماندووبوون یان کەمی وزە دەکەم.\",\"کێشەم هەیە لە خەوتن یان زۆر دەخەوم.\",\"گۆڕانکاریم لە ئارەزووی خواردن یان کێشدا هەیە.\",\"هەست بە بێ بەها بوون یان تاوانبارکردنی خۆم دەکەم.\",\"کێشەم هەیە لە سەرنجدان یان بڕیاردان.\",\"لە ئاساییدا خاوتر جووڵە دەدەم یان قسە دەکەم.\",\"بیرکردنەوەم لە مردن یان خۆکوشتن هەیە.\",\"هەست بە بێ هیوایی دەکەم سەبارەت بە داهاتوو.\"]', 'نمرە ٠-١٠: خەمۆکی کەم، ١١-٢٥: خەمۆکی سووک بۆ مامناوەند، ٢٦-٤٠: خەمۆکی توند - پێویستە یارمەتی پسپۆڕانە بخوازیت', NOW(), NOW()),
(3, 'Anxiety Test', 'دڵەڕاوکێ', 'هەڵسەنگاندن بۆ نیشانەکانی دڵەڕاوکێ', '[\"هەست بە دڵەڕاوکێ، نیگەرانی، یان گرژی دەکەم.\",\"ناتوانم نیگەرانی بوەستێنم یان کۆنترۆڵی بکەم.\",\"زۆر نیگەران دەبم سەبارەت بە شتە جیاوازەکان.\",\"کێشەم هەیە لە ئارامگرتن.\",\"هێندە بێئارامم کە سەختە بە ئارامی دانیشم.\",\"بە ئاسانی تووڕە یان پەشۆکاو دەبم.\",\"هەست بە ترس دەکەم وەک ئەوەی شتێکی خراپ ڕوو بدات.\",\"نیشانە جەستەییەکانی وەک ئارەقکردن یان لەرزینم هەیە.\",\"دوور دەکەومەوە لە دۆخەکان کە دڵەڕاوکێم دەکەن.\",\"دڵەڕاوکێیەکەم تێکەڵی چالاکییە ڕۆژانەکانم دەکات.\"]', 'نمرە ٠-١٠: دڵەڕاوکێی کەم، ١١-٢٥: دڵەڕاوکێی سووک بۆ مامناوەند، ٢٦-٤٠: دڵەڕاوکێی توند - پێویستە یارمەتی پسپۆڕانە بخوازیت', NOW(), NOW()),
(4, 'Stress Test', 'فشاری(سترێسی) دەروونی', 'هەڵسەنگاندنی ئاستی فشاری دەروونی', '[\"هەست دەکەم بەرپرسیارێتییەکانم زۆرن بۆم.\",\"کێشەم هەیە لە بەڕێوەبردنی کاتەکەم بە شێوەیەکی کاریگەر.\",\"هەست بە گرژی یان پەشۆکاوی دەکەم.\",\"سەختە ئارام بگرمەوە دوای ڕۆژێکی پڕ فشار.\",\"پەشۆکاو دەبم لەسەر شتەکان کە لە دەرەوەی کۆنترۆڵمن.\",\"نیشانە جەستەییەکانی فشاری دەروونیم هەیە (سەرئێشە، گرژی ماسولکە).\",\"هەست دەکەم ناتوانم ڕووبەڕووی هەموو ئەو شتانە ببمەوە کە دەبێت بیکەم.\",\"کێشەم هەیە لە خەوتن بەهۆی فشاری دەروونییەوە.\",\"هەست بە تووڕەیی یان کورت تەمەنی دەکەم.\",\"ڕێگای نەتەندروستی بەکاردەهێنم بۆ ڕووبەڕووبوونەوە (زۆر خواردن، بەکارهێنانی مادە).\"]', 'نمرە ٠-١٠: فشاری کەم، ١١-٢٥: فشاری مامناوەند، ٢٦-٤٠: فشاری زۆر - پێویستە تەکنیکەکانی بەڕێوەبردنی فشار بەکاربهێنیت', NOW(), NOW()),
(5, 'Social Anxiety Test', 'دڵەڕاوکێی کۆمەڵایەتی', 'هەڵسەنگاندن بۆ نیشانەکانی دڵەڕاوکێی کۆمەڵایەتی', '[\"لە دۆخە کۆمەڵایەتییەکاندا هەست بە دڵەڕاوکێ دەکەم.\",\"نیگەران دەبم لەوەی کەسانی تر حوکمم لەسەر بدەن.\",\"دوور دەکەومەوە لە کۆبوونەوە یان بۆنە کۆمەڵایەتییەکان.\",\"لە دۆخە کۆمەڵایەتییەکاندا نیشانە جەستەییەکانم هەیە (سووربوونەوە، ئارەقکردن).\",\"دەترسم لەوەی خۆم شەرمەزار بکەم لەبەردەم کەسانی تر.\",\"کێشەم هەیە لە چاو لە چاو بوون لەگەڵ خەڵک.\",\"دوور دەکەومەوە لە قسەکردن لە گرووپ یان کۆبوونەوەکاندا.\",\"ڕۆژان پێش دۆخە کۆمەڵایەتییەکان نیگەران دەبم.\",\"هەست بە خۆئاگایی دەکەم سەبارەت بە ڕواڵەت یان ڕەفتارەکەم.\",\"ترسە کۆمەڵایەتییەکانم تێکەڵی کار یان پەیوەندییەکانم دەکات.\"]', 'نمرە ٠-١٠: دڵەڕاوکێی کۆمەڵایەتی کەم، ١١-٢٥: دڵەڕاوکێی کۆمەڵایەتی مامناوەند، ٢٦-٤٠: دڵەڕاوکێی کۆمەڵایەتی توند - پێویستە چارەسەری بخوازیت', NOW(), NOW()),
(6, 'Healthy Lifestyle Test', 'شێوازی ژیانی تەندروست', 'هەڵسەنگاندنی شێوازی ژیانی تەندروست', '[\"بە ڕێژەیەکی باش خواردنی تەندروست دەخۆم.\",\"بە بەردەوامی وەرزش دەکەم (لانیکەم ٣ جار لە هەفتەیەکدا).\",\"خەوێکی پێویست دەکەم (٧-٩ کاتژمێر لە شەودا).\",\"ئاوی پێویست دەخۆمەوە (لانیکەم ٨ گیلاس لە ڕۆژێکدا).\",\"دوور دەکەومەوە لە خواردنی پڕ چەوری و شەکر.\",\"کاتی پێویست بۆ ئارامگرتن و خۆپارێزی تەرخان دەکەم.\",\"بە باشی لەگەڵ فشاری ژیان مامەڵە دەکەم.\",\"پشوودانی مەعیدە و دەروونی بەردەوام دەکەم.\",\"دوور دەکەومەوە لە جگەرە و کحول.\",\"پەیوەندی کۆمەڵایەتی باشم هەیە لەگەڵ خێزان و هاوڕێکان.\"]', 'نمرە ٠-١٠: شێوازی ژیانی خراپ، ١١-٢٥: شێوازی ژیانی مامناوەند، ٢٦-٤٠: شێوازی ژیانی زۆر تەندروست', NOW(), NOW()),
(7, 'Memory Test', 'یادگە', 'هەڵسەنگاندنی توانای یادگە', '[\"بە ئاسانی ناوی کەسان لەبیر دەکەم.\",\"زۆرجار شوێنی دانانی شتەکانم لەبیر دەچێت.\",\"کێشەم هەیە لە یادکردنەوەی ژمارە تەلەفۆنەکان.\",\"بە ئاسانی ڕووداوەکانی ڕابردووی نزیک لەبیر دەهێنمەوە.\",\"زۆرجار لەبیرم دەچێت کە چیم کردووە یان کوێم چووە.\",\"کێشەم هەیە لە یادکردنەوەی وشە یان ناوەکان.\",\"بە باشی دەتوانم زانیاری نوێ فێر بم.\",\"زۆرجار شت لەبیرم دەچێت کە دەبوو بیکەم.\",\"کێشەم هەیە لە یادکردنەوەی ڕێگاکان یان ئاڕاستەکان.\",\"بە باشی دەتوانم لە یەک کاتدا چەند شت لە یادمدا بهێڵمەوە.\"]', 'نمرە ٠-١٠: یادگەی باش، ١١-٢٥: یادگەی مامناوەند، ٢٦-٤٠: کێشەی یادگە - پێویستە پشکنینی پزیشکی بکەیت', NOW(), NOW()),
(8, 'Emotional Intelligence Test', 'زیرەکی سۆزداری', 'هەڵسەنگاندنی زیرەکی سۆزداری', '[\"بە باشی هەستەکانی خۆم دەناسم و تێیان دەگەم.\",\"دەتوانم هەستەکانی خۆم بە باشی کۆنترۆڵ بکەم.\",\"بە باشی هەستەکانی کەسانی تر دەناسمەوە.\",\"دەتوانم بە کاریگەری لەگەڵ کەسانی تر پەیوەندی دروست بکەم.\",\"بە باشی گوێ لە کەسانی تر دەگرم.\",\"دەتوانم ملکەچی و لێبوردەکار بم.\",\"بە باشی لەگەڵ گۆڕانکاری مامەڵە دەکەم.\",\"دەتوانم کەسانی تر هان بدەم و پاڵپشتیان بکەم.\",\"بە باشی لە تیمدا کار دەکەم.\",\"دەتوانم ڕەخنە بە شێوەیەکی دروست وەربگرم.\"]', 'نمرە ٠-١٠: زیرەکی سۆزداری کەم، ١١-٢٥: زیرەکی سۆزداری مامناوەند، ٢٦-٤٠: زیرەکی سۆزداری بەرز', NOW(), NOW()),
(9, 'Type A Personality Test', 'کەسایەتی جۆری A', 'هەڵسەنگاندنی کەسایەتی جۆری A', '[\"زۆرجار پەلە دەکەم و بە خێرایی هەڵسوکەوت دەکەم.\",\"بە سەختی چاوەڕوانی دەکەم و بێ ئارامم کاتێک شتەکان خاو دەبن.\",\"زۆرجار لە یەک کاتدا چەند کار دەکەم.\",\"هەست دەکەم هەمیشە لە پێشبڕکێیەکدام.\",\"بە ئاسانی تووڕە دەبم لە کاتی ترافیک یان ڕیزەکان.\",\"زۆرجار کەسانی تر دەبڕمەوە کاتێک قسە دەکەن.\",\"هەمیشە هەوڵ دەدەم زیاتر لە کەسانی تر بەدەست بهێنم.\",\"کێشەم هەیە لە ئارامگرتن و پشوودان.\",\"زۆرجار دەستم دەکەم بە مشت یان پەنجە لەسەر مێز.\",\"هەست دەکەم کات هەمیشە کەمە.\"]', 'نمرە ٠-١٠: کەسایەتی جۆری B، ١١-٢٥: کەسایەتی تێکەڵ، ٢٦-٤٠: کەسایەتی جۆری A - مەترسی نەخۆشی دڵ زیاترە', NOW(), NOW()),
(10, 'OCD Test', 'وەسواسی (OCD)', 'هەڵسەنگاندن بۆ نیشانەکانی وەسواسی', '[\"بیرکردنەوە یان وێنەی ناخۆشم هەیە کە ناتوانم ڕێگری لێ بکەم.\",\"هەست دەکەم دەبێت شتەکان بە ڕێکی دیاریکراو ڕێک بخەم.\",\"زۆرجار دەستم دەشۆم یان پاکم دەکەمەوە.\",\"چەندین جار پشکنینی دەرگا یان ئاگرەکان دەکەم.\",\"هەست دەکەم دەبێت کارەکان بە شێوەیەکی دیاریکراو ئەنجام بدەم.\",\"نیگەران دەبم لەوەی زیانی کەسانی تر بگەیەنم.\",\"زۆرجار ژمارەکان یان وشەکان لە مێشکمدا دووبارە دەکەمەوە.\",\"کۆکردنەوە یان هەڵگرتنی شتە بێ پێویستەکان.\",\"زۆر کات بۆ ئەنجامدانی کارە ئاساییەکان دەخایەنم.\",\"ئەم ڕەفتارانە تێکەڵی ژیانی ڕۆژانەم دەکەن.\"]', 'نمرە ٠-١٠: وەسواسی کەم، ١١-٢٥: وەسواسی مامناوەند، ٢٦-٤٠: وەسواسی توند - پێویستە چارەسەری بخوازیت', NOW(), NOW()),
(11, 'Video Game Addiction Test', 'ئالوودەبوونی یاری ئەلکترۆنی', 'هەڵسەنگاندن بۆ ئالوودەبوونی یاری ئەلکترۆنی', '[\"زۆربەی کاتەکەم بە یاری ئەلکترۆنی بەسەر دەبەم.\",\"نیگەران دەبم یان تووڕە دەبم کاتێک ناتوانم یاری بکەم.\",\"یاری کردن لە کار یان قوتابخانە کاریگەری نەرێنی هەیە.\",\"درۆ دەکەم سەبارەت بە کاتێک کە بۆ یاری تەرخان دەکەم.\",\"یاری وەک ڕێگەیەک بەکاردەهێنم بۆ دوورکەوتنەوە لە کێشەکان.\",\"هەوڵی کەمکردنەوەی یاریم داوە بەڵام سەرکەوتوو نەبووم.\",\"پەیوەندی کۆمەڵایەتی و خێزانیم لەبەر یاری زیانی بینیوە.\",\"خەوی کەم دەکەم بەهۆی یاری کردنەوە.\",\"هەست بە پێویستی زیادکردنی کاتی یاری دەکەم.\",\"بێ یاری هەست بە بێزاری و نائارامی دەکەم.\"]', 'نمرە ٠-١٠: بەکارهێنانی ئاسایی، ١١-٢٥: بەکارهێنانی زۆر، ٢٦-٤٠: ئالوودەبوون - پێویستە یارمەتی بخوازیت', NOW(), NOW()),
(12, 'PTSD Test', 'تێکچوونی فشاری دەروونی دوای زەبر (PTSD)', 'هەڵسەنگاندن بۆ نیشانەکانی PTSD', '[\"خەونی خراپم هەیە سەبارەت بە ڕووداوە کارەساتبارەکە.\",\"یادەوەری ڕووداوەکە لەناکاو دێتەوە مێشکم.\",\"دوور دەکەومەوە لە شوێن یان کەسانی وەک ڕووداوەکە.\",\"هەست بە بێهەستی یان جیابوونەوە لە کەسانی تر دەکەم.\",\"زۆر ئاگادارم و هەمیشە چاوەڕوانی مەترسی دەکەم.\",\"بە ئاسانی دەتۆقم یان دەپەشۆکێم.\",\"کێشەم هەیە لە خەوتن یان لە خەو مانەوە.\",\"هەست بە تووڕەیی یان هەڕەشە دەکەم.\",\"کێشەم هەیە لە سەرنجدان یان یادکردنەوە.\",\"هەست بە تاوانبارکردنی خۆم دەکەم سەبارەت بە ڕووداوەکە.\"]', 'نمرە ٠-١٠: نیشانەی کەم، ١١-٢٥: نیشانەی مامناوەند، ٢٦-٤٠: نیشانەی توند - پێویستە چارەسەری تایبەتمەند بخوازیت', NOW(), NOW()),
(13, 'Borderline Personality Disorder Test', 'تێکچوونی کەسایەتی گوماناویی', 'هەڵسەنگاندن بۆ نیشانەکانی تێکچوونی کەسایەتی گوماناویی', '[\"زۆر ترسم لەوەی کەسان بەجێم بهێڵن.\",\"پەیوەندییەکانم ناجێگیرن و پڕ گۆڕانکارین.\",\"هەست بە نادیاری سەبارەت بە خۆم دەکەم.\",\"کارە مەترسیدارەکان دەکەم کاتێک هەستیار دەبم.\",\"هەڵسوکەوتی خۆئەزیەتدان یان هەڕەشەی خۆکوشتنم هەیە.\",\"کەشوهەوای دەروونیم بە خێرایی دەگۆڕێت.\",\"هەست بە بەتاڵی یان پووچەڵی دەکەم.\",\"تووڕەیی توندم هەیە کە سەختە کۆنترۆڵی بکەم.\",\"لە کاتی فشاردا هەست بە پارانۆیا یان جیابوونەوە دەکەم.\",\"هەوڵ دەدەم بە هەر شێوەیەک پەیوەندییەکان ڕابگرم.\"]', 'نمرە ٠-١٠: نیشانەی کەم، ١١-٢٥: نیشانەی مامناوەند، ٢٦-٤٠: نیشانەی توند - پێویستە چارەسەری تایبەتمەند بخوازیت', NOW(), NOW()),
(14, 'ADHD Test', 'تێکچوونی سەرنج-کەمی/زۆر چالاکیی (ADHD)', 'هەڵسەنگاندن بۆ نیشانەکانی ADHD', '[\"کێشەم هەیە لە سەرنجدان بە وردەکاریەکان.\",\"کێشەم هەیە لە تەواوکردنی ئەرکەکان.\",\"کێشەم هەیە لە ڕێکخستن و پلاندانان.\",\"دوور دەکەومەوە لە ئەرکە سەختەکان.\",\"زۆرجار شتەکانم لەدەست دەچێت.\",\"بە ئاسانی سەرنجم لاودەچێت.\",\"زۆرجار لەبیرم دەچێت شتەکان بکەم.\",\"بێئارامم و ناتوانم بە ئارامی دانیشم.\",\"زۆر قسە دەکەم یان کەسان دەبڕمەوە.\",\"کێشەم هەیە لە چاوەڕوانی کردن.\"]', 'نمرە ٠-١٠: نیشانەی کەم، ١١-٢٥: نیشانەی مامناوەند، ٢٦-٤٠: نیشانەی توند - پێویستە هەڵسەنگاندنی پزیشکی بکەیت', NOW(), NOW()),
(15, 'Agoraphobia Test', 'ئاگۆرافۆبیا', 'هەڵسەنگاندن بۆ نیشانەکانی ئاگۆرافۆبیا', '[\"دەترسم لە بوون لە شوێنە قەرەباڵغەکان.\",\"دوور دەکەومەوە لە گواستنەوەی گشتی.\",\"دەترسم لە بوون لە شوێنە داخراوەکان.\",\"دەترسم لە بوون لە ڕیزەکان یان کۆمەڵەکان.\",\"دەترسم لە بوون بە تەنها لە دەرەوەی ماڵەوە.\",\"دەترسم لە بوون لە شوێنە بەرفراوانەکان.\",\"پێویستم بە کەسێک هەیە لەگەڵم بێت کاتێک دەچمە دەرەوە.\",\"دوور دەکەومەوە لە چوونە شوێنە نوێیەکان.\",\"نیشانە جەستەییەکانی ترسم هەیە لە شوێنە دیاریکراوەکان.\",\"ئەم ترسانە کاریگەری لەسەر ژیانی ڕۆژانەم هەیە.\"]', 'نمرە ٠-١٠: ترسی کەم، ١١-٢٥: ترسی مامناوەند، ٢٦-٤٠: ئاگۆرافۆبیای توند - پێویستە چارەسەری بخوازیت', NOW(), NOW()),
(16, 'Therapy Need Assessment', 'ئایا پێویستم بە چارەسەرکارە', 'هەڵسەنگاندن بۆ پێویستی چارەسەری', '[\"هەستەکانم کاریگەری لەسەر کار یان قوتابخانەم هەیە.\",\"کێشەم هەیە لە پەیوەندی لەگەڵ کەسانی تر.\",\"بەردەوام هەست بە خەمۆکی یان دڵەڕاوکێ دەکەم.\",\"کێشەم هەیە لە خەوتن یان خواردن.\",\"هەست دەکەم ناتوانم لەگەڵ کێشەکانم مامەڵە بکەم.\",\"بیرکردنەوەم لە خۆئەزیەتدان یان خۆکوشتن هەیە.\",\"ڕەفتارەکانم گۆڕاون و کەسانی تر تێبینیان کردووە.\",\"هەست بە بێ هیوایی یان بێ ئومێدی دەکەم.\",\"بەکارهێنانی مادەی هۆشبەر یان کحولم زیادکردووە.\",\"هەست دەکەم پێویستم بە یارمەتی هەیە بەڵام نازانم چۆن بیدۆزمەوە.\"]', 'نمرە ٠-١٠: پێویستی کەم، ١١-٢٥: پێویستی مامناوەند - چارەسەری سوودبەخشە، ٢٦-٤٠: پێویستی زۆر - پێویستە دەستبەجێ چارەسەری بخوازیت', NOW(), NOW());

-- --------------------------------------------------------

--
-- Table structure for table `test_results`
--

CREATE TABLE `test_results` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `test_id` int(11) NOT NULL,
  `answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`answers`)),
  `total_score` int(11) NOT NULL,
  `completed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `credits` int(11) DEFAULT 0,
  `role` enum('Admin','User','Doctor','Psychologist','Counsellor') DEFAULT 'User',
  `profile_photo` varchar(255) DEFAULT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `token_expiry` datetime DEFAULT NULL,
  `remember_me_token` varchar(255) DEFAULT NULL,
  `remember_me_expiry` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `credits`, `role`, `profile_photo`, `reset_token`, `token_expiry`, `remember_me_token`, `remember_me_expiry`, `created_at`) VALUES
(1, 'mohammed', '<EMAIL>', '$2y$10$qYzSyboYEvaInEHn8RevOukyJToxwH23s8Z/KXq1kAWpaguVTNAY2', 7, 'Admin', NULL, NULL, NULL, 'ed80d7a79bf5673649f68f78596326fd', '2024-11-30 14:05:40', '2024-10-29 13:21:39'),
(2, 'ihsan', '<EMAIL>', '$2y$10$jhvLAZ92ne8oxK/qjcl8VODM.M0UlFcFSm//b37/tvahzrcXMgpLC', 2, 'Counsellor', NULL, NULL, NULL, NULL, NULL, '2024-10-29 13:40:36'),
(3, 'د. ئازاد', '<EMAIL>', '$2y$10$a87YH0pY4JW6e/SDK.pAw./UbXodzg3GPst3bot4AOtfIvvm64A2.', 1, 'Doctor', 'uploads/profile_photos/3_profile.png', NULL, NULL, '36f82a7cc8d22cb463a5b6a9ce67b72b', '2024-12-03 15:37:20', '2024-10-29 14:36:41'),
(4, 'MTAG96', '<EMAIL>', '$2y$10$V4dii/VRiVx2XGbTn.nDaeBugD3j6QZpg/BBOnUtTYN80juN/jW46', 0, 'User', 'uploads/profile_photos/4_profile.jpg', NULL, NULL, 'ad796dda130bafeee18eb80d6f38d049', '2024-11-30 13:09:27', '2024-10-31 12:05:16'),
(5, 'haiv', '<EMAIL>', '$2y$10$C.7hYJWErEUq6h87/BwsP.KGgAOnSIUucVEFF6ErPGBZVeT7goTvu', 0, 'User', NULL, NULL, NULL, 'c31972e71640f66b942a4f47995ab838', '2024-11-30 13:18:28', '2024-10-31 12:18:11');

-- --------------------------------------------------------

--
-- Table structure for table `users_schedule`
--

CREATE TABLE `users_schedule` (
  `schedule_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `day_of_week` enum('Saturday','Sunday','Monday','Tuesday','Wednesday','Thursday','Friday') NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users_schedule`
--

INSERT INTO `users_schedule` (`schedule_id`, `user_id`, `day_of_week`, `start_time`, `end_time`) VALUES
(2, 3, 'Friday', '14:00:00', '15:00:00'),
(6, 3, 'Saturday', '13:00:00', '14:00:00'),
(7, 3, 'Saturday', '17:00:00', '18:00:00'),
(8, 3, 'Wednesday', '13:00:00', '21:00:00'),
(10, 2, 'Sunday', '21:32:00', '22:32:00'),
(11, 2, 'Saturday', '21:00:00', '22:00:00'),
(12, 2, 'Saturday', '22:14:00', '23:30:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `purchases`
--
ALTER TABLE `purchases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `tests`
--
ALTER TABLE `tests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `test_results`
--
ALTER TABLE `test_results`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `test_id` (`test_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD PRIMARY KEY (`schedule_id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `purchases`
--
ALTER TABLE `purchases`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tests`
--
ALTER TABLE `tests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `test_results`
--
ALTER TABLE `test_results`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `users_schedule`
--
ALTER TABLE `users_schedule`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `test_results`
--
ALTER TABLE `test_results`
  ADD CONSTRAINT `test_results_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `test_results_ibfk_2` FOREIGN KEY (`test_id`) REFERENCES `tests` (`id`);

--
-- Constraints for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD CONSTRAINT `users_schedule_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
