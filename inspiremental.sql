-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 29, 2025 at 09:48 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `inspiremental`
--

-- --------------------------------------------------------

--
-- Table structure for table `purchases`
--

CREATE TABLE `purchases` (
  `id` int(11) <PERSON>SIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `purchase_amount` int(11) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_status` enum('pending','completed','failed') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `transaction_type` enum('purchase','usage') DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `credits` int(11) DEFAULT 0,
  `role` enum('Admin','User','Doctor','Psychologist','Counsellor') DEFAULT 'User',
  `profile_photo` varchar(255) DEFAULT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `token_expiry` datetime DEFAULT NULL,
  `remember_me_token` varchar(255) DEFAULT NULL,
  `remember_me_expiry` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `credits`, `role`, `profile_photo`, `reset_token`, `token_expiry`, `remember_me_token`, `remember_me_expiry`, `created_at`) VALUES
(1, 'mohammed', '<EMAIL>', '$2y$10$qYzSyboYEvaInEHn8RevOukyJToxwH23s8Z/KXq1kAWpaguVTNAY2', 7, 'Admin', NULL, NULL, NULL, 'ed80d7a79bf5673649f68f78596326fd', '2024-11-30 14:05:40', '2024-10-29 13:21:39'),
(2, 'ihsan', '<EMAIL>', '$2y$10$jhvLAZ92ne8oxK/qjcl8VODM.M0UlFcFSm//b37/tvahzrcXMgpLC', 2, 'Counsellor', NULL, NULL, NULL, NULL, NULL, '2024-10-29 13:40:36'),
(3, 'د. ئازاد', '<EMAIL>', '$2y$10$a87YH0pY4JW6e/SDK.pAw./UbXodzg3GPst3bot4AOtfIvvm64A2.', 1, 'Doctor', 'uploads/profile_photos/3_profile.png', NULL, NULL, '36f82a7cc8d22cb463a5b6a9ce67b72b', '2024-12-03 15:37:20', '2024-10-29 14:36:41'),
(4, 'MTAG96', '<EMAIL>', '$2y$10$V4dii/VRiVx2XGbTn.nDaeBugD3j6QZpg/BBOnUtTYN80juN/jW46', 0, 'User', 'uploads/profile_photos/4_profile.jpg', NULL, NULL, 'ad796dda130bafeee18eb80d6f38d049', '2024-11-30 13:09:27', '2024-10-31 12:05:16'),
(5, 'haiv', '<EMAIL>', '$2y$10$C.7hYJWErEUq6h87/BwsP.KGgAOnSIUucVEFF6ErPGBZVeT7goTvu', 0, 'User', NULL, NULL, NULL, 'c31972e71640f66b942a4f47995ab838', '2024-11-30 13:18:28', '2024-10-31 12:18:11');

-- --------------------------------------------------------

--
-- Table structure for table `users_schedule`
--

CREATE TABLE `users_schedule` (
  `schedule_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `day_of_week` enum('Saturday','Sunday','Monday','Tuesday','Wednesday','Thursday','Friday') NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users_schedule`
--

INSERT INTO `users_schedule` (`schedule_id`, `user_id`, `day_of_week`, `start_time`, `end_time`) VALUES
(2, 3, 'Friday', '14:00:00', '15:00:00'),
(6, 3, 'Saturday', '13:00:00', '14:00:00'),
(7, 3, 'Saturday', '17:00:00', '18:00:00'),
(8, 3, 'Wednesday', '13:00:00', '21:00:00'),
(10, 2, 'Sunday', '21:32:00', '22:32:00'),
(11, 2, 'Saturday', '21:00:00', '22:00:00'),
(12, 2, 'Saturday', '22:14:00', '23:30:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `purchases`
--
ALTER TABLE `purchases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD PRIMARY KEY (`schedule_id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `purchases`
--
ALTER TABLE `purchases`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `users_schedule`
--
ALTER TABLE `users_schedule`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD CONSTRAINT `users_schedule_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
