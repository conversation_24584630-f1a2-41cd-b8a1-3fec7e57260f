<?php
session_start();
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Check if transactions table exists, if not create a simple query that won't fail
$table_check = $con->query("SHOW TABLES LIKE 'transactions'");
if ($table_check->num_rows > 0) {
    // Fetch transactions from the database
    $query = "SELECT t.id, u.username, t.amount, t.transaction_type, t.description, t.created_at
              FROM transactions t
              JOIN users u ON t.user_id = u.id
              ORDER BY t.created_at DESC";
    $result = $con->query($query);
} else {
    $result = null;
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>بەڕێوەبردنی مامەڵەکان - ئیلهامبەخشی دەروونی</title>
    <?php include 'head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(font/Rabar_22.ttf);
        }
        @font-face {
            font-family: RabarBold;
            src: url(font/Rabar_21.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .transactions-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table th {
            background: linear-gradient(135deg, #ed7014, #ff8c00);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        .table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-color: #f0f0f0;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
            margin: 0 2px;
        }
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }
        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }
        @media (max-width: 768px) {
            .admin-header {
                padding: 20px 0;
            }
            .admin-header h1 {
                font-size: 1.5rem;
            }
            .table-responsive {
                font-size: 0.875rem;
            }
            .btn-sm {
                padding: 4px 8px;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
<?php include 'admin_navbar.php'; ?>

<div class="container mt-4">
    <div class="transactions-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ژمارە</th>
                        <th>بەکارهێنەر</th>
                        <th>بڕ</th>
                        <th>جۆر</th>
                        <th>وەسف</th>
                        <th>بەروار</th>
                        <th>کردارەکان</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result && $result->num_rows > 0): ?>
                        <?php while ($transaction = $result->fetch_assoc()): ?>
                            <tr>
                                <td><span class="badge bg-primary"><?php echo $transaction['id']; ?></span></td>
                                <td><strong><?php echo htmlspecialchars($transaction['username']); ?></strong></td>
                                <td>
                                    <span class="<?php echo ($transaction['amount'] >= 0) ? 'amount-positive' : 'amount-negative'; ?>">
                                        <?php echo ($transaction['amount'] >= 0 ? '+' : '') . number_format($transaction['amount'], 2); ?> دینار
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $type_kurdish = [
                                        'deposit' => 'داناندن',
                                        'withdrawal' => 'وەرگرتن',
                                        'payment' => 'پارەدان',
                                        'refund' => 'گەڕاندنەوە'
                                    ];
                                    $type = strtolower($transaction['transaction_type']);
                                    ?>
                                    <span class="badge bg-info"><?php echo $type_kurdish[$type] ?? ucfirst($transaction['transaction_type']); ?></span>
                                </td>
                                <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y/m/d H:i', strtotime($transaction['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <a href="delete_transaction.php?id=<?php echo $transaction['id']; ?>" class="btn btn-danger btn-sm"
                                       onclick="return confirm('ئایا دڵنیایت لە سڕینەوەی ئەم مامەڵەیە؟');">
                                        <i class="fas fa-trash me-1"></i> سڕینەوە
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">هیچ مامەڵەیەک نەدۆزرایەوە</h5>
                                <p class="text-muted">هێشتا هیچ مامەڵەیەک تۆمار نەکراوە</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include 'script.php'; ?>
</body>
</html>
