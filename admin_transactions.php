<?php
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Fetch transactions from the database
$query = "SELECT t.id, u.username, t.amount, t.transaction_type, t.description, t.created_at 
          FROM transactions t
          JOIN users u ON t.user_id = u.id
          ORDER BY t.created_at DESC";
$result = $con->query($query);
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>Admin - Manage Transactions</title>
    <?php include 'head.php'; ?>
</head>
<body>
<?php include 'admin_navbar.php'; ?>

<div class="container mt-5">
    <h1>Manage Transactions</h1>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>User</th>
                <th>Amount</th>
                <th>Type</th>
                <th>Description</th>
                <th>Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($result->num_rows > 0): ?>
                <?php while ($transaction = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $transaction['id']; ?></td>
                        <td><?php echo $transaction['username']; ?></td>
                        <td><?php echo $transaction['amount']; ?></td>
                        <td><?php echo ucfirst($transaction['transaction_type']); ?></td>
                        <td><?php echo $transaction['description']; ?></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($transaction['created_at'])); ?></td>
                        <td>
                            <!-- Delete button (optional) -->
                            <a href="delete_transaction.php?id=<?php echo $transaction['id']; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this transaction?');">Delete</a>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="text-center">No transactions found.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php include 'script.php'; ?>
</body>
</html>
