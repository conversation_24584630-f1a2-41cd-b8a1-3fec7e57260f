<?php
session_start();
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Get statistics for dashboard
$users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$tests_count = $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'];
$transactions_count = $con->query("SELECT COUNT(*) as count FROM transactions")->fetch_assoc()['count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>پانێڵی بەڕێوەبەر - ئیلهامبەخشی دەروونی</title>
    <?php include 'head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(font/Rabar_22.ttf);
        }
        @font-face {
            font-family: RabarBold;
            src: url(font/Rabar_21.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .admin-header {
            background: linear-gradient(135deg, #265e6d, #2e7d8a);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #265e6d;
            margin-bottom: 10px;
        }
        .stats-label {
            color: #666;
            font-size: 1.1rem;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .admin-card:hover {
            transform: translateY(-5px);
            border-color: #ed7014;
            box-shadow: 0 10px 25px rgba(237, 112, 20, 0.2);
        }
        .admin-card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #265e6d;
        }
        .admin-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .admin-card-desc {
            color: #666;
            margin-bottom: 20px;
        }
        .btn-admin {
            background: linear-gradient(135deg, #ed7014, #ff8c00);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-admin:hover {
            background: linear-gradient(135deg, #ff8c00, #ed7014);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(237, 112, 20, 0.3);
        }
        @media (max-width: 768px) {
            .admin-header {
                padding: 20px 0;
            }
            .stats-number {
                font-size: 2rem;
            }
            .admin-card {
                padding: 20px;
            }
            .admin-card-icon {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
<?php include 'admin_navbar.php'; ?>



<div class="container py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4 col-sm-6">
            <div class="stats-card">
                <div class="stats-number"><?php echo $users_count; ?></div>
                <div class="stats-label">بەکارهێنەر</div>
            </div>
        </div>
        <div class="col-md-4 col-sm-6">
            <div class="stats-card">
                <div class="stats-number"><?php echo $tests_count; ?></div>
                <div class="stats-label">تاقیکردنەوە</div>
            </div>
        </div>
        <div class="col-md-4 col-sm-6">
            <div class="stats-card">
                <div class="stats-number"><?php echo $transactions_count; ?></div>
                <div class="stats-label">مامەڵە</div>
            </div>
        </div>
    </div>

    <!-- Admin Management Cards -->
    <div class="row">
        <div class="col-lg-4 col-md-6">
            <div class="admin-card">
                <div class="admin-card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="admin-card-title">بەڕێوەبردنی بەکارهێنەران</div>
                <div class="admin-card-desc">بینین، دەستکاری، و بەڕێوەبردنی هەموو بەکارهێنەرانی سیستەم</div>
                <a href="admin_users.php" class="btn-admin">بەڕێوەبردن</a>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="admin-card">
                <div class="admin-card-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="admin-card-title">بەڕێوەبردنی تاقیکردنەوەکان</div>
                <div class="admin-card-desc">زیادکردن، دەستکاری، و بەڕێوەبردنی تاقیکردنەوە دەروونییەکان</div>
                <a href="admin_tests.php" class="btn-admin">بەڕێوەبردن</a>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="admin-card">
                <div class="admin-card-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="admin-card-title">بەڕێوەبردنی مامەڵەکان</div>
                <div class="admin-card-desc">بینین و بەڕێوەبردنی هەموو مامەڵە داراییەکان</div>
                <a href="admin_transactions.php" class="btn-admin">بەڕێوەبردن</a>
            </div>
        </div>
    </div>
</div>

<?php include 'script.php'; ?>
</body>
</html>
