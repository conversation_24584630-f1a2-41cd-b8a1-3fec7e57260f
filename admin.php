<?php
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>Admin Dashboard</title>
    <?php include 'head.php'; ?>
</head>
<body>
<?php include 'admin_navbar.php'; ?>

<div class="container mt-5">
    <h1>Welcome to the Admin Dashboard</h1>
    <p>Manage users, transactions, and more from here.</p>
    <div class="row">
        <div class="col-md-4">
            <a href="admin_users" class="btn btn-primary btn-block">Manage Users</a>
        </div>
        <div class="col-md-4">
            <a href="admin_transactions" class="btn btn-primary btn-block">Manage Transactions</a>
        </div>
        <!-- Add more admin features as needed -->
    </div>
</div>

<?php include 'script.php'; ?>
</body>
</html>
