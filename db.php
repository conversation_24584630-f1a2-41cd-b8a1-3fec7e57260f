<?php
// For development (local):
$servername = "localhost";  // Keep this as "localhost" if running locally
$username = "root";         // Default MySQL username for local development
$password = "";             // Default MySQL password for local development (blank for XAMPP/MAMP)
$dbname = "inspiremental";  // Database name you created locally

// $servername = "localhost"; // This will be provided by your hosting provider
// $username = "yaridagr_inspiremental";   // Database username from your hosting provider
// $password = ".y$4Tx}25x7&";   // Database password from your hosting provider
// $dbname = "yaridagr_inspiremental";         // Name of the database created on the hosting server

// Create connection
$con = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($con->connect_error) {
    die("Connection failed: " . $con->connect_error);
}

// Set character set to avoid encoding issues (recommended for multilingual sites)
$con->set_charset("utf8mb4");
?>
