<style>
  .test-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: center;
    position: relative;
    overflow: hidden;
  }

  .test-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(237, 112, 20, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .test-card .textBox {
    position: relative;
    transition: all 0.3s ease;
    border-radius: 15px !important;
    border: 2px solid transparent;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  }

  .test-card:hover .textBox {
    background: linear-gradient(135deg, #ed7014 0%, #ff8c00 100%);
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  .test-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
    z-index: 1;
    pointer-events: none;
  }

  .test-card:hover::before {
    left: 100%;
  }

  .test-card .textBox {
    position: relative;
    z-index: 2;
  }

  .test-card:nth-child(odd):hover {
    animation: wobble 0.6s ease-in-out;
  }

  .test-card:nth-child(even):hover {
    animation: pulse 0.6s ease-in-out;
  }

  @keyframes wobble {
    0%, 100% { transform: translateY(-8px) scale(1.02) rotate(0deg); }
    25% { transform: translateY(-8px) scale(1.02) rotate(1deg); }
    75% { transform: translateY(-8px) scale(1.02) rotate(-1deg); }
  }

  @keyframes pulse {
    0%, 100% { transform: translateY(-8px) scale(1.02); }
    50% { transform: translateY(-8px) scale(1.08); }
  }

  .test-card:hover .textBox::after {
    content: '✨';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.2em;
    animation: sparkle 1s infinite;
  }

  @keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  /* Special hover effect for the first test */
  .test-card:first-child:hover .textBox {
    background: linear-gradient(135deg, goldenrod 0%, #ffd700 100%);
    color: #333 !important;
  }
</style>

<div class="container centered text-center">
  <div class="row row-cols-1 row-cols-xs-2 row-cols-sm-2 row-cols-md-4 centered g-1">
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=1">
        <div class="textBox centered p-3" style="background-color: goldenrod; color: beige;">تەندروستی دەروونیت ئەمڕۆ</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=6">
        <div class="textBox centered p-3">شێوازی ژیانی تەندروست</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=2">
        <div class="textBox centered p-3">خەمۆکی</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=16">
        <div class="textBox centered p-3">ئایا پێویستم بە چارەسەرکارە</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=7">
        <div class="textBox centered p-3">یادگە</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=8">
        <div class="textBox centered p-3">زیرەکی سۆزداری</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=4">
        <div class="textBox centered p-3">فشاری(سترێسی) دەروونی</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=5">
        <div class="textBox centered p-3">دڵەڕاوکێی کۆمەڵایەتی</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=9">
        <div class="textBox centered p-3">کەسایەتی جۆری A</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=10">
        <div class="textBox centered p-3">وەسواسی (OCD)</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=11">
        <div class="textBox centered p-3">ئالوودەبوونی یاری ئەلکترۆنی</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=12">
        <div class="textBox centered p-3">تێکچوونی فشاری دەروونی دوای زەبر (PTSD)</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=13">
        <div class="textBox centered p-3">تێکچوونی کەسایەتی گوماناویی</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=3">
        <div class="textBox centered p-3">دڵەڕاوکێ</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=14">
        <div class="textBox centered p-3">تێکچوونی سەرنج-کەمی/زۆر چالاکیی (ADHD)</div>
      </a>
    </div>
    <div class="test col test-card">
      <a class="testLabel" href="test-handler.php?test_id=15">
        <div class="textBox centered p-3">ئاگۆرافۆبیا</div>
      </a>
    </div>
  </div>
</div>