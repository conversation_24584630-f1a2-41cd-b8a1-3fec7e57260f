RewriteEngine On
RewriteBase /inspiremental/
# RewriteBase https://inspire.mental.mtag96.site/

# Redirect base URL to /home
RewriteRule ^$ /inspiremental/home [R=301,L]
# RewriteRule ^$ https://inspire.mental.mtag96.site/home [R=301,L]

# Redirect from /index.php?page=home to /home
RewriteCond %{THE_REQUEST} \s/index\.php\?page=home\s [NC]
RewriteRule ^ /home? [R=301,L]

# Redirect from /index.php?page=about to /about
RewriteCond %{THE_REQUEST} \s/index\.php\?page=([^\s&]+)\s [NC]
RewriteRule ^ /%1? [R=301,L]

# Rewrite /home to index.php?page=home
RewriteRule ^home$ index.php?page=home [L]

# Rewrite other pages to their clean URL format
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)$ index.php?page=$1 [L]

# Rewrite URLs without .php extension
RewriteCond /%{REQUEST_FILENAME}.php -f
RewriteRule ^([^/]+)/?$ $1.php [L]
