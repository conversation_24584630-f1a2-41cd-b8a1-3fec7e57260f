<?php
session_start();
include 'db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: index.php');
    exit();
}

// Fetch users from the database
$result = $con->query("SELECT id, username, email, role FROM users");
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>بەڕێوەبردنی بەکارهێنەران - ئیلهامبەخشی دەروونی</title>
    <?php include 'head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(font/Rabar_22.ttf);
        }
        @font-face {
            font-family: RabarBold;
            src: url(font/Rabar_21.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .users-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table th {
            background: linear-gradient(135deg, #ed7014, #ff8c00);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        .table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-color: #f0f0f0;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
            margin: 0 2px;
        }
        @media (max-width: 768px) {
            .admin-header {
                padding: 20px 0;
            }
            .admin-header h1 {
                font-size: 1.5rem;
            }
            .table-responsive {
                font-size: 0.875rem;
            }
            .btn-sm {
                padding: 4px 8px;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
<?php include 'admin_navbar.php'; ?>

<div class="container py-4">
    <div class="users-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ژمارە</th>
                        <th>ناوی بەکارهێنەر</th>
                        <th>ئیمەیڵ</th>
                        <th>ڕۆڵ</th>
                        <th>کردارەکان</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result->num_rows > 0): ?>
                        <?php while ($user = $result->fetch_assoc()): ?>
                            <tr>
                                <td><span class="badge bg-primary"><?php echo $user['id']; ?></span></td>
                                <td><strong><?php echo htmlspecialchars($user['username']); ?></strong></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <?php if ($user['role'] === 'Admin'): ?>
                                        <span class="badge bg-danger">بەڕێوەبەر</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">بەکارهێنەر</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="admin_edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit me-1"></i> دەستکاری
                                    </a>
                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <a href="delete_user.php?id=<?php echo $user['id']; ?>" class="btn btn-danger btn-sm"
                                           onclick="return confirm('ئایا دڵنیایت لە سڕینەوەی ئەم بەکارهێنەرە؟');">
                                            <i class="fas fa-trash me-1"></i> سڕینەوە
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">هیچ بەکارهێنەرێک نەدۆزرایەوە</h5>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include 'script.php'; ?>
</body>
</html>
